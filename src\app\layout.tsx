import "./globals.css";
import { ClientProviders } from './client-providers'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: {
    default: 'BookScribe AI - AI-Powered Novel Writing Platform',
    template: '%s | BookScribe AI'
  },
  description: 'Create epic novels with AI assistance. BookScribe helps you write, edit, and publish books with intelligent story development, character creation, and consistency tracking.',
  keywords: ['AI writing', 'novel writing software', 'book writing tool', 'AI author assistant', 'story generator', 'creative writing AI'],
  authors: [{ name: 'BookScribe AI' }],
  creator: 'BookScribe AI',
  publisher: 'BookScribe AI',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://bookscribe.ai',
    siteName: 'BookScribe AI',
    title: 'BookScribe AI - AI-Powered Novel Writing Platform',
    description: 'Create epic novels with AI assistance. Write up to 300,000 words with perfect consistency.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'BookScribe AI - Write Your Next Bestseller',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BookScribe AI - AI-Powered Novel Writing Platform',
    description: 'Create epic novels with AI assistance. Write up to 300,000 words with perfect consistency.',
    images: ['/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600&display=swap" rel="stylesheet" />
      </head>
      <body>
        <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md">
          Skip to main content
        </a>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}