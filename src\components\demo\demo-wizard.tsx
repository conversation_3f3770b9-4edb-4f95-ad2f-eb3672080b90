"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  ChevronLeft, 
  ChevronRight, 
  Sparkles, 
  BookOpen, 
  Users, 
  Globe,
  Target,
  Settings,
  CheckCircle
} from "lucide-react";

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
}

const wizardSteps: WizardStep[] = [
  { id: "basics", title: "Project Basics", description: "Set up your project foundation", icon: BookOpen },
  { id: "genre", title: "Genre & Style", description: "Define your story's genre and tone", icon: <PERSON><PERSON><PERSON> },
  { id: "characters", title: "Characters & World", description: "Create your story universe", icon: Users },
  { id: "structure", title: "Structure & Pacing", description: "Plan your narrative flow", icon: Target },
  { id: "technical", title: "Technical Specs", description: "Set word counts and chapters", icon: Settings },
  { id: "review", title: "Review & Generate", description: "Finalize and create your project", icon: CheckCircle }
];

export function DemoWizard() {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    title: "The Crystal Kingdoms",
    description: "A fantasy epic about a young mage discovering ancient crystal magic",
    genre: "fantasy",
    subgenre: "epic-fantasy",
    tone: "adventurous",
    targetAudience: "young-adult",
    protagonist: "Aria Stormwind",
    setting: "Mystical realm of Aethermoor",
    wordCount: "80000",
    chapters: "25"
  });

  const progress = ((currentStep + 1) / wizardSteps.length) * 100;

  const nextStep = () => {
    if (currentStep < wizardSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between mb-4">
            <CardTitle className="text-2xl flex items-center gap-2">
              <Sparkles className="w-6 h-6 text-primary" />
              Project Creation Wizard
            </CardTitle>
            <Badge variant="outline" className="border-primary/50 text-primary">
              Interactive Demo
            </Badge>
          </div>
          
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Step {currentStep + 1} of {wizardSteps.length}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step Navigation */}
          <div className="flex items-center gap-2 mt-4 overflow-x-auto pb-2">
            {wizardSteps.map((step, index) => {
              const StepIcon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              
              return (
                <div
                  key={step.id}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm whitespace-nowrap cursor-pointer transition-all ${
                    isActive 
                      ? 'bg-amber-500/20 text-primary border border-amber-500/30' 
                      : isCompleted
                      ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                      : 'bg-muted/50 text-muted-foreground border border-border hover:bg-muted/70'
                  }`}
                  onClick={() => setCurrentStep(index)}
                >
                  <StepIcon className="w-4 h-4" />
                  <span className="hidden sm:inline">{step.title}</span>
                </div>
              );
            })}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Current Step Content */}
          <div className="min-h-[400px]">
            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-2">{wizardSteps[currentStep].title}</h3>
              <p className="text-muted-foreground">{wizardSteps[currentStep].description}</p>
            </div>

            {currentStep === 0 && <BasicsStep formData={formData} updateFormData={updateFormData} />}
            {currentStep === 1 && <GenreStep formData={formData} updateFormData={updateFormData} />}
            {currentStep === 2 && <CharactersStep formData={formData} updateFormData={updateFormData} />}
            {currentStep === 3 && <StructureStep formData={formData} updateFormData={updateFormData} />}
            {currentStep === 4 && <TechnicalStep formData={formData} updateFormData={updateFormData} />}
            {currentStep === 5 && <ReviewStep formData={formData} />}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6 border-t border-border">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="border-border text-white hover:bg-muted/70"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>
            
            <Button
              onClick={nextStep}
              disabled={currentStep === wizardSteps.length - 1}
              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
            >
              {currentStep === wizardSteps.length - 1 ? "Generate Project" : "Next"}
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Step Components
function BasicsStep({ formData, updateFormData }: { formData: any; updateFormData: (field: string, value: string) => void }) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="title">Project Title</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => updateFormData("title", e.target.value)}
          className="bg-muted/50 border-border"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="description">Story Concept</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => updateFormData("description", e.target.value)}
          className="bg-muted/50 border-border min-h-[100px]"
          placeholder="Describe your story idea..."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="audience">Target Audience</Label>
        <Select value={formData.targetAudience} onValueChange={(value) => updateFormData("targetAudience", value)}>
          <SelectTrigger className="bg-muted/50 border-border">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="middle-grade">Middle Grade (8-12)</SelectItem>
            <SelectItem value="young-adult">Young Adult (13-17)</SelectItem>
            <SelectItem value="new-adult">New Adult (18-25)</SelectItem>
            <SelectItem value="adult">Adult (25+)</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

function GenreStep({ formData, updateFormData }: { formData: any; updateFormData: (field: string, value: string) => void }) {
  const genres = [
    { id: "fantasy", name: "Fantasy", subgenres: ["Epic Fantasy", "Urban Fantasy", "Dark Fantasy", "High Fantasy"] },
    { id: "sci-fi", name: "Science Fiction", subgenres: ["Space Opera", "Cyberpunk", "Dystopian", "Hard Sci-Fi"] },
    { id: "romance", name: "Romance", subgenres: ["Contemporary", "Historical", "Paranormal", "Romantic Suspense"] },
    { id: "mystery", name: "Mystery", subgenres: ["Cozy Mystery", "Police Procedural", "Noir", "Psychological Thriller"] }
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Primary Genre</Label>
        <div className="grid grid-cols-2 gap-3">
          {genres.map((genre) => (
            <Card
              key={genre.id}
              className={`cursor-pointer transition-all border ${
                formData.genre === genre.id
                  ? 'border-primary/50 bg-primary/10'
                  : 'border-border bg-card hover:border-border'
              }`}
              onClick={() => updateFormData("genre", genre.id)}
            >
              <CardContent className="p-4 text-center">
                <h4 className="font-medium">{genre.name}</h4>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {formData.genre && (
        <div className="space-y-2">
          <Label>Subgenre</Label>
          <Select value={formData.subgenre} onValueChange={(value) => updateFormData("subgenre", value)}>
            <SelectTrigger className="bg-muted/50 border-border">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {genres.find(g => g.id === formData.genre)?.subgenres.map((sub) => (
                <SelectItem key={sub} value={sub.toLowerCase().replace(/\s+/g, '-')}>
                  {sub}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="space-y-2">
        <Label>Tone & Mood</Label>
        <div className="flex flex-wrap gap-2">
          {["Adventurous", "Dark", "Humorous", "Romantic", "Mysterious", "Epic"].map((tone) => (
            <Badge
              key={tone}
              variant={formData.tone === tone.toLowerCase() ? "default" : "outline"}
              className={`cursor-pointer ${
                formData.tone === tone.toLowerCase()
                  ? 'bg-amber-500 text-black'
                  : 'border-border text-white hover:bg-muted/70'
              }`}
              onClick={() => updateFormData("tone", tone.toLowerCase())}
            >
              {tone}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
}

function CharactersStep({ formData, updateFormData }: { formData: any; updateFormData: (field: string, value: string) => void }) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="protagonist">Main Protagonist</Label>
        <Input
          id="protagonist"
          value={formData.protagonist}
          onChange={(e) => updateFormData("protagonist", e.target.value)}
          className="bg-muted/50 border-border"
          placeholder="e.g., Aria Stormwind"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="setting">Primary Setting</Label>
        <Input
          id="setting"
          value={formData.setting}
          onChange={(e) => updateFormData("setting", e.target.value)}
          className="bg-muted/50 border-border"
          placeholder="e.g., Mystical realm of Aethermoor"
        />
      </div>

      <div className="space-y-2">
        <Label>Character Archetypes</Label>
        <div className="grid grid-cols-2 gap-3">
          {[
            { name: "The Hero", desc: "Brave protagonist on a journey" },
            { name: "The Mentor", desc: "Wise guide and teacher" },
            { name: "The Shadow", desc: "Primary antagonist" },
            { name: "The Ally", desc: "Loyal companion" }
          ].map((archetype) => (
            <Card key={archetype.name} className="border-border bg-card hover:border-border cursor-pointer">
              <CardContent className="p-4">
                <h4 className="font-medium text-sm">{archetype.name}</h4>
                <p className="text-xs text-muted-foreground mt-1">{archetype.desc}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="p-4 bg-primary/10 border border-primary/20 rounded-lg">
        <div className="flex items-start gap-3">
          <Sparkles className="w-5 h-5 text-primary mt-0.5" />
          <div>
            <h4 className="font-medium text-primary">AI Character Generator</h4>
            <p className="text-sm text-primary/80 mt-1">
              Our AI will automatically generate detailed character profiles, backstories, and relationships based on your selections.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function StructureStep({ formData, updateFormData }: { formData: any; updateFormData: (field: string, value: string) => void }) {
  const structures = [
    { id: "three-act", name: "Three-Act Structure", desc: "Classic beginning, middle, end" },
    { id: "heros-journey", name: "Hero's Journey", desc: "Campbell's monomyth structure" },
    { id: "seven-point", name: "Seven-Point Story", desc: "Dan Wells' structure method" },
    { id: "save-the-cat", name: "Save the Cat", desc: "Blake Snyder's beat sheet" }
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Story Structure</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {structures.map((structure) => (
            <Card
              key={structure.id}
              className="cursor-pointer transition-all border border-border bg-card hover:border-border"
            >
              <CardContent className="p-4">
                <h4 className="font-medium">{structure.name}</h4>
                <p className="text-sm text-muted-foreground mt-1">{structure.desc}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <Label>Pacing Preference</Label>
        <div className="flex gap-2">
          {["Slow Burn", "Moderate", "Fast-Paced", "Action-Packed"].map((pace) => (
            <Badge
              key={pace}
              variant="outline"
              className="cursor-pointer border-border text-white hover:bg-muted/70"
            >
              {pace}
            </Badge>
          ))}
        </div>
      </div>

      <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <div className="flex items-start gap-3">
          <Target className="w-5 h-5 text-blue-500 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-400">AI Structure Planning</h4>
            <p className="text-sm text-blue-200/80 mt-1">
              Our Story Architect agent will create a detailed outline with plot points, character arcs, and pacing guidelines.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function TechnicalStep({ formData, updateFormData }: { formData: any; updateFormData: (field: string, value: string) => void }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="wordCount">Target Word Count</Label>
          <Select value={formData.wordCount} onValueChange={(value) => updateFormData("wordCount", value)}>
            <SelectTrigger className="bg-muted/50 border-border">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="50000">50,000 words (Novella)</SelectItem>
              <SelectItem value="80000">80,000 words (Standard Novel)</SelectItem>
              <SelectItem value="120000">120,000 words (Epic Novel)</SelectItem>
              <SelectItem value="200000">200,000+ words (Series)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="chapters">Estimated Chapters</Label>
          <Input
            id="chapters"
            type="number"
            value={formData.chapters}
            onChange={(e) => updateFormData("chapters", e.target.value)}
            className="bg-muted/50 border-border"
            min="1"
            max="100"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Chapter Length Preference</Label>
        <div className="flex gap-2">
          {["Short (1-2k)", "Medium (3-4k)", "Long (5-6k)", "Variable"].map((length) => (
            <Badge
              key={length}
              variant="outline"
              className="cursor-pointer border-border text-white hover:bg-muted/70"
            >
              {length}
            </Badge>
          ))}
        </div>
      </div>

      <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
        <div className="flex items-start gap-3">
          <Settings className="w-5 h-5 text-green-500 mt-0.5" />
          <div>
            <h4 className="font-medium text-green-400">Smart Planning</h4>
            <p className="text-sm text-green-200/80 mt-1">
              AI will optimize chapter breaks, pacing, and word distribution for maximum reader engagement.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function ReviewStep({ formData }: { formData: any }) {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">Project Ready for Generation!</h3>
        <p className="text-muted-foreground">Review your selections below and generate your project.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="border-border bg-card">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Project Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><span className="text-muted-foreground">Title:</span> {formData.title}</div>
            <div><span className="text-muted-foreground">Genre:</span> {formData.genre}</div>
            <div><span className="text-muted-foreground">Target:</span> {formData.targetAudience}</div>
            <div><span className="text-muted-foreground">Word Count:</span> {parseInt(formData.wordCount).toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card className="border-border bg-card">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Story Elements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><span className="text-muted-foreground">Protagonist:</span> {formData.protagonist}</div>
            <div><span className="text-muted-foreground">Setting:</span> {formData.setting}</div>
            <div><span className="text-muted-foreground">Tone:</span> {formData.tone}</div>
            <div><span className="text-muted-foreground">Chapters:</span> {formData.chapters}</div>
          </CardContent>
        </Card>
      </div>

      <div className="p-6 bg-gradient-to-r from-primary/10 to-primary/10 border border-primary/20 rounded-lg">
        <div className="text-center">
          <Sparkles className="w-8 h-8 text-primary mx-auto mb-3" />
          <h4 className="font-semibold text-primary mb-2">AI Generation Process</h4>
          <p className="text-sm text-primary/80 mb-4">
            Our AI agents will now create your complete story structure, character profiles, world details, and chapter outlines.
          </p>
          <div className="flex justify-center gap-4 text-xs text-muted-foreground">
            <span>• Story Architecture</span>
            <span>• Character Development</span>
            <span>• World Building</span>
            <span>• Chapter Planning</span>
          </div>
        </div>
      </div>
    </div>
  );
}
