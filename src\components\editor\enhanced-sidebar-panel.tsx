'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useResizablePanel } from '@/hooks/use-resizable-panel'
import { createClient } from '@/lib/supabase/client'
import {
  Send,
  X,
  Bot,
  User,
  Trash2,
  BookOpen,
  Users,
  MapPin,
  Lightbulb,
  Plus,
  Edit3,
  Save,
  Feather,
  Brain,
  FileText,
  MessageSquare,
  Star,
  TrendingUp,
  History,
  Layers,
  GitBranch,
  PanelRightClose,
  PanelRightOpen
} from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import { KnowledgeInputPanel } from './knowledge-input-panel'
import { LazyStoryBiblePanel, LazyVersionHistoryPanel, LazyAiChatPanel } from '@/components/lazy'
import { BookSummaryGenerator } from '@/components/analysis/book-summary-generator'
import { CharacterArcVisualizer } from '@/components/analysis/character-arc-visualizer'
import { PlotHoleDetector } from '@/components/analysis/plot-hole-detector'
import { PacingAnalyzer } from '@/components/analysis/pacing-analyzer'
import { EmotionalJourneyMapper } from '@/components/analysis/emotional-journey-mapper'

interface EnhancedSidebarPanelProps {
  projectId: string
  chapterId?: string
  selectedText?: string
  userId?: string
  onClose?: () => void
  initialWidth?: number
  onWidthChange?: (width: number) => void
  defaultTab?: string
  onTabChange?: (tab: string) => void
}

export function EnhancedSidebarPanel({
  projectId,
  chapterId,
  selectedText,
  userId,
  onClose,
  initialWidth = 440,
  onWidthChange,
  defaultTab = 'knowledge',
  onTabChange
}: EnhancedSidebarPanelProps) {
  const { width, isResizing, ResizeHandle } = useResizablePanel({
    initialWidth,
    minWidth: 320,
    maxWidth: 800,
    onResize: onWidthChange
  })

  const [activeTab, setActiveTab] = useState(defaultTab)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [activeAnalysisTab, setActiveAnalysisTab] = useState('summary')
  const supabase = createClient()

  // Handle collapsed state
  if (isCollapsed) {
    return (
      <div className="h-full w-12 border-l bg-background flex flex-col items-center py-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(false)}
          className="mb-4"
        >
          <PanelRightOpen className="h-4 w-4" />
        </Button>
        
        <div className="flex-1 flex flex-col items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsCollapsed(false)
              setActiveTab('knowledge')
            }}
            className="p-2"
          >
            <Brain className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsCollapsed(false)
              setActiveTab('summaries')
            }}
            className="p-2"
          >
            <FileText className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsCollapsed(false)
              setActiveTab('story-bible')
            }}
            className="p-2"
          >
            <BookOpen className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsCollapsed(false)
              setActiveTab('ai-chat')
            }}
            className="p-2"
          >
            <MessageSquare className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div
      className="h-full flex flex-col bg-background border-l relative panel-transition resizable-panel panel-shadow"
      style={{ width }}
    >
      <ResizeHandle />

      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Project Tools</h2>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(true)}
            >
              <PanelRightClose className="h-4 w-4" />
            </Button>
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => { setActiveTab(value); onTabChange?.(value); }} className="h-full flex flex-col content-fade">
          <TabsList className="grid w-full grid-cols-5 mx-4 mt-2">
            <TabsTrigger value="knowledge" className="text-xs">
              <Brain className="h-3 w-3 mr-1" />
              Knowledge
            </TabsTrigger>
            <TabsTrigger value="summaries" className="text-xs">
              <FileText className="h-3 w-3 mr-1" />
              Summary
            </TabsTrigger>
            <TabsTrigger value="analysis" className="text-xs">
              <TrendingUp className="h-3 w-3 mr-1" />
              Analysis
            </TabsTrigger>
            <TabsTrigger value="story-bible" className="text-xs">
              <BookOpen className="h-3 w-3 mr-1" />
              Bible
            </TabsTrigger>
            <TabsTrigger value="ai-chat" className="text-xs">
              <MessageSquare className="h-3 w-3 mr-1" />
              AI Chat
            </TabsTrigger>
          </TabsList>

          {/* Knowledge Base Tab */}
          <TabsContent value="knowledge" className="flex-1 mt-0 overflow-hidden tab-content-enter-active">
            <KnowledgeInputPanel
              projectId={projectId}
              chapterId={chapterId}
              selectedText={selectedText}
              initialWidth={width}
            />
          </TabsContent>

          {/* Summaries Tab */}
          <TabsContent value="summaries" className="flex-1 mt-2 overflow-hidden tab-content-enter-active">
            <div className="h-full px-4 pb-4 overflow-auto custom-scrollbar">
              <BookSummaryGenerator
                projectId={projectId}
                className="h-full"
              />
            </div>
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis" className="flex-1 mt-0 overflow-hidden tab-content-enter-active">
            <div className="h-full flex flex-col">
              <Tabs value={activeAnalysisTab} onValueChange={setActiveAnalysisTab} className="h-full flex flex-col">
                <TabsList className="grid w-full grid-cols-4 mx-4 mt-2">
                  <TabsTrigger value="arcs" className="text-xs">Arcs</TabsTrigger>
                  <TabsTrigger value="pacing" className="text-xs">Pacing</TabsTrigger>
                  <TabsTrigger value="emotions" className="text-xs">Emotions</TabsTrigger>
                  <TabsTrigger value="issues" className="text-xs">Issues</TabsTrigger>
                </TabsList>

                <TabsContent value="arcs" className="flex-1 mt-2 overflow-hidden">
                  <div className="h-full px-4 pb-4 overflow-auto">
                    <CharacterArcVisualizer
                      projectId={projectId}
                      className="h-full"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="pacing" className="flex-1 mt-2 overflow-hidden">
                  <div className="h-full px-4 pb-4 overflow-auto">
                    <PacingAnalyzer
                      projectId={projectId}
                      className="h-full"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="emotions" className="flex-1 mt-2 overflow-hidden">
                  <div className="h-full px-4 pb-4 overflow-auto">
                    <EmotionalJourneyMapper
                      projectId={projectId}
                      className="h-full"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="issues" className="flex-1 mt-2 overflow-hidden">
                  <div className="h-full px-4 pb-4 overflow-auto">
                    <PlotHoleDetector
                      projectId={projectId}
                      className="h-full"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </TabsContent>

          {/* Story Bible Tab */}
          <TabsContent value="story-bible" className="flex-1 mt-0 overflow-hidden tab-content-enter-active">
            <LazyStoryBiblePanel
              projectId={projectId}
            />
          </TabsContent>

          {/* AI Chat Tab */}
          <TabsContent value="ai-chat" className="flex-1 mt-0 overflow-hidden tab-content-enter-active">
            <LazyAiChatPanel
              projectId={projectId}
              chapterId={chapterId}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}