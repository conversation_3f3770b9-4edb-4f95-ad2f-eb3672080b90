/**
 * Customization Hub Component
 * Comprehensive visual customization interface combining theme settings and showcase
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Palette, 
  Sun, 
  Moon, 
  Monitor, 
  Check,
  Sparkles,
  Settings
} from 'lucide-react';

import { useBookScribeTheme } from '@/hooks/use-bookscribe-theme';
import { getAllThemes, getThemesByMode } from '@/lib/themes/theme-registry';
import { useThemeSettings } from '@/lib/settings/settings-store';

interface ThemePreviewCardProps {
  themeId: string;
  themeName: string;
  themeDescription: string;
  themeMode: 'light' | 'dark';
  colors: {
    background: string;
    text: string;
    accent: string;
    card: string;
  };
  isActive: boolean;
  onSelect: () => void;
}

function ThemePreviewCard({ 
  themeId, 
  themeName, 
  themeDescription, 
  themeMode, 
  colors, 
  isActive, 
  onSelect 
}: ThemePreviewCardProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
        isActive ? 'ring-2 ring-primary shadow-lg' : 'hover:ring-1 hover:ring-border'
      }`}
      onClick={onSelect}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {themeMode === 'light' ? (
              <Sun className="h-4 w-4 text-amber-600" />
            ) : (
              <Moon className="h-4 w-4 text-blue-400" />
            )}
            <CardTitle className="text-sm font-medium">{themeName}</CardTitle>
          </div>
          {isActive && (
            <Check className="h-4 w-4 text-primary" />
          )}
        </div>
        <CardDescription className="text-xs">{themeDescription}</CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Color Preview */}
        <div 
          className="rounded-lg p-4 mb-3 border"
          style={{ 
            backgroundColor: colors.background,
            borderColor: colors.accent + '40'
          }}
        >
          <div 
            className="text-xs font-medium mb-2"
            style={{ color: colors.text }}
          >
            Sample Text
          </div>
          <div className="flex gap-1">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors.accent }}
            />
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors.card }}
            />
            <div 
              className="w-3 h-3 rounded-full opacity-60"
              style={{ backgroundColor: colors.text }}
            />
          </div>
        </div>
        
        <Badge variant={themeMode === 'light' ? 'default' : 'secondary'} className="text-xs">
          {themeMode}
        </Badge>
      </CardContent>
    </Card>
  );
}

export function CustomizationHub() {
  const { switchToTheme, isThemeActive, theme, mounted } = useBookScribeTheme();
  const { theme: themeSettings, updateTheme } = useThemeSettings();

  if (!mounted) {
    return (
      <div className="space-y-8">
        {/* Loading skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-3 bg-muted rounded w-full" />
              </CardHeader>
              <CardContent>
                <div className="h-16 bg-muted rounded mb-3" />
                <div className="h-5 bg-muted rounded w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const lightThemes = getThemesByMode('light');
  const darkThemes = getThemesByMode('dark');

  const handleThemeModeChange = (mode: 'light' | 'dark' | 'system') => {
    updateTheme({ themeMode: mode });
    
    if (mode === 'system') {
      switchToTheme('system');
    } else {
      // Switch to default theme for the selected mode
      const defaultTheme = mode === 'light' ? lightThemes[0] : darkThemes[0];
      if (defaultTheme) {
        switchToTheme(defaultTheme.id);
      }
    }
  };

  return (
    <div className="space-y-8">
      {/* Theme Mode Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Theme Mode Preference
          </CardTitle>
          <CardDescription>
            Choose how BookScribe AI should determine which theme to use
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={themeSettings.themeMode}
            onValueChange={handleThemeModeChange}
            className="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="light" id="light" />
              <Label htmlFor="light" className="flex items-center gap-2 cursor-pointer flex-1">
                <Sun className="w-4 h-4 text-amber-600" />
                <div>
                  <div className="font-medium">Light Mode</div>
                  <div className="text-xs text-muted-foreground">Always use light themes</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="dark" id="dark" />
              <Label htmlFor="dark" className="flex items-center gap-2 cursor-pointer flex-1">
                <Moon className="w-4 h-4 text-blue-400" />
                <div>
                  <div className="font-medium">Dark Mode</div>
                  <div className="text-xs text-muted-foreground">Always use dark themes</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="system" id="system" />
              <Label htmlFor="system" className="flex items-center gap-2 cursor-pointer flex-1">
                <Monitor className="w-4 h-4" />
                <div>
                  <div className="font-medium">System</div>
                  <div className="text-xs text-muted-foreground">Follow system preference</div>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      <Separator />

      {/* Theme Gallery Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Palette className="h-5 w-5 text-primary" />
          <h2 className="text-2xl font-bold">Theme Gallery</h2>
        </div>
        <p className="text-muted-foreground">
          Choose from our collection of carefully crafted writing themes
        </p>
      </div>

      {/* Light Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Sun className="h-4 w-4 text-amber-600" />
          <h3 className="text-lg font-semibold">Light Themes</h3>
          <Badge variant="outline">{lightThemes.length}</Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {lightThemes.map((themeData) => (
            <ThemePreviewCard
              key={themeData.id}
              themeId={themeData.id}
              themeName={themeData.name}
              themeDescription={themeData.description}
              themeMode={themeData.mode}
              colors={{
                background: themeData.colors.background,
                text: themeData.colors.text,
                accent: themeData.colors.accent,
                card: themeData.colors.window
              }}
              isActive={isThemeActive(themeData.id)}
              onSelect={() => {
                switchToTheme(themeData.id);
                updateTheme({ currentTheme: themeData.id, themeMode: 'light' });
              }}
            />
          ))}
        </div>
      </div>

      <Separator />

      {/* Dark Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Moon className="h-4 w-4 text-blue-400" />
          <h3 className="text-lg font-semibold">Dark Themes</h3>
          <Badge variant="outline">{darkThemes.length}</Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {darkThemes.map((themeData) => (
            <ThemePreviewCard
              key={themeData.id}
              themeId={themeData.id}
              themeName={themeData.name}
              themeDescription={themeData.description}
              themeMode={themeData.mode}
              colors={{
                background: themeData.colors.background,
                text: themeData.colors.text,
                accent: themeData.colors.accent,
                card: themeData.colors.window
              }}
              isActive={isThemeActive(themeData.id)}
              onSelect={() => {
                switchToTheme(themeData.id);
                updateTheme({ currentTheme: themeData.id, themeMode: 'dark' });
              }}
            />
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Quickly switch between popular theme combinations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchToTheme('writers-sanctuary-light');
                updateTheme({ currentTheme: 'writers-sanctuary-light', themeMode: 'light' });
              }}
            >
              <Sun className="w-3 w-3 mr-1" />
              Default Light
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchToTheme('evening-study-dark');
                updateTheme({ currentTheme: 'evening-study-dark', themeMode: 'dark' });
              }}
            >
              <Moon className="w-3 w-3 mr-1" />
              Default Dark
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchToTheme('system');
                updateTheme({ themeMode: 'system' });
              }}
            >
              <Monitor className="w-3 w-3 mr-1" />
              Auto
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
