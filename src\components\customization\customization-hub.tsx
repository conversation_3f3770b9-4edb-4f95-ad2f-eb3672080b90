/**
 * Customization Hub Component
 * Comprehensive visual customization interface combining theme settings and showcase
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Palette,
  Sun,
  Moon,
  Monitor,
  Check,
  Sparkles,
  Settings,
  Type,
  Plus,
  Edit,
  Save,
  Trash2
} from 'lucide-react';

import { useBookScribeTheme } from '@/hooks/use-bookscribe-theme';
import { getAllThemes, getThemesByMode } from '@/lib/themes/theme-registry';
import { useThemeSettings, useTypographySettings } from '@/lib/settings/settings-store';
import { textSizeMap, fontFamilyMap } from '@/lib/settings/settings-types';

interface ThemePreviewCardProps {
  themeId: string;
  themeName: string;
  themeDescription: string;
  themeMode: 'light' | 'dark';
  colors: {
    background: string;
    text: string;
    accent: string;
    card: string;
  };
  isActive: boolean;
  onSelect: () => void;
}

function ThemePreviewCard({ 
  themeId, 
  themeName, 
  themeDescription, 
  themeMode, 
  colors, 
  isActive, 
  onSelect 
}: ThemePreviewCardProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
        isActive ? 'ring-2 ring-primary shadow-lg' : 'hover:ring-1 hover:ring-border'
      }`}
      onClick={onSelect}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {themeMode === 'light' ? (
              <Sun className="h-4 w-4 text-amber-600" />
            ) : (
              <Moon className="h-4 w-4 text-blue-400" />
            )}
            <CardTitle className="text-sm font-medium">{themeName}</CardTitle>
          </div>
          {isActive && (
            <Check className="h-4 w-4 text-primary" />
          )}
        </div>
        <CardDescription className="text-xs">{themeDescription}</CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Color Preview */}
        <div 
          className="rounded-lg p-4 mb-3 border"
          style={{ 
            backgroundColor: colors.background,
            borderColor: colors.accent + '40'
          }}
        >
          <div 
            className="text-xs font-medium mb-2"
            style={{ color: colors.text }}
          >
            Sample Text
          </div>
          <div className="flex gap-1">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors.accent }}
            />
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors.card }}
            />
            <div 
              className="w-3 h-3 rounded-full opacity-60"
              style={{ backgroundColor: colors.text }}
            />
          </div>
        </div>
        
        <Badge variant={themeMode === 'light' ? 'default' : 'secondary'} className="text-xs">
          {themeMode}
        </Badge>
      </CardContent>
    </Card>
  );
}

export function CustomizationHub() {
  const { switchToTheme, isThemeActive, theme, mounted } = useBookScribeTheme();
  const { theme: themeSettings, updateTheme } = useThemeSettings();
  const { typography, updateTypography } = useTypographySettings();
  const [customSize, setCustomSize] = useState(typography.customTextSize || 14);
  const [activeTab, setActiveTab] = useState('themes');

  if (!mounted) {
    return (
      <div className="space-y-8">
        {/* Loading skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-3 bg-muted rounded w-full" />
              </CardHeader>
              <CardContent>
                <div className="h-16 bg-muted rounded mb-3" />
                <div className="h-5 bg-muted rounded w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const lightThemes = getThemesByMode('light');
  const darkThemes = getThemesByMode('dark');

  const handleThemeModeChange = (mode: 'light' | 'dark' | 'system') => {
    updateTheme({ themeMode: mode });
    
    if (mode === 'system') {
      switchToTheme('system');
    } else {
      // Switch to default theme for the selected mode
      const defaultTheme = mode === 'light' ? lightThemes[0] : darkThemes[0];
      if (defaultTheme) {
        switchToTheme(defaultTheme.id);
      }
    }
  };

  const textSizeOptions = [
    { value: 'small', label: 'Small', description: 'Compact text' },
    { value: 'medium', label: 'Medium', description: 'Standard size' },
    { value: 'large', label: 'Large', description: 'Larger text' },
    { value: 'extra-large', label: 'Extra Large', description: 'Maximum size' },
    { value: 'custom', label: 'Custom', description: 'Set your own' },
  ] as const;

  const handleCustomSizeChange = (value: number[]) => {
    const newSize = value[0];
    setCustomSize(newSize);
    updateTypography({
      textSize: 'custom',
      customTextSize: newSize
    });
  };

  return (
    <div className="space-y-8">
      {/* Customization Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="themes" className="flex items-center gap-2">
            <Palette className="w-4 h-4" />
            Visual Themes
          </TabsTrigger>
          <TabsTrigger value="typography" className="flex items-center gap-2">
            <Type className="w-4 h-4" />
            Typography
          </TabsTrigger>
        </TabsList>

        <TabsContent value="themes" className="space-y-8 mt-6">
          {/* Theme Mode Selection */}
          <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Theme Mode Preference
          </CardTitle>
          <CardDescription>
            Choose how BookScribe AI should determine which theme to use
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={themeSettings.themeMode}
            onValueChange={handleThemeModeChange}
            className="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="light" id="light" />
              <Label htmlFor="light" className="flex items-center gap-2 cursor-pointer flex-1">
                <Sun className="w-4 h-4 text-amber-600" />
                <div>
                  <div className="font-medium">Light Mode</div>
                  <div className="text-xs text-muted-foreground">Always use light themes</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="dark" id="dark" />
              <Label htmlFor="dark" className="flex items-center gap-2 cursor-pointer flex-1">
                <Moon className="w-4 h-4 text-blue-400" />
                <div>
                  <div className="font-medium">Dark Mode</div>
                  <div className="text-xs text-muted-foreground">Always use dark themes</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="system" id="system" />
              <Label htmlFor="system" className="flex items-center gap-2 cursor-pointer flex-1">
                <Monitor className="w-4 h-4" />
                <div>
                  <div className="font-medium">System</div>
                  <div className="text-xs text-muted-foreground">Follow system preference</div>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      <Separator />

      {/* Theme Gallery Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Palette className="h-5 w-5 text-primary" />
          <h2 className="text-2xl font-bold">Theme Gallery</h2>
        </div>
        <p className="text-muted-foreground">
          Choose from our collection of carefully crafted writing themes
        </p>
      </div>

      {/* Light Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Sun className="h-4 w-4 text-amber-600" />
          <h3 className="text-lg font-semibold">Light Themes</h3>
          <Badge variant="outline">{lightThemes.length}</Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {lightThemes.map((themeData) => (
            <ThemePreviewCard
              key={themeData.id}
              themeId={themeData.id}
              themeName={themeData.name}
              themeDescription={themeData.description}
              themeMode={themeData.mode}
              colors={{
                background: themeData.colors.background,
                text: themeData.colors.text,
                accent: themeData.colors.accent,
                card: themeData.colors.window
              }}
              isActive={isThemeActive(themeData.id)}
              onSelect={() => {
                switchToTheme(themeData.id);
                updateTheme({ currentTheme: themeData.id, themeMode: 'light' });
              }}
            />
          ))}
        </div>
      </div>

      <Separator />

      {/* Dark Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Moon className="h-4 w-4 text-blue-400" />
          <h3 className="text-lg font-semibold">Dark Themes</h3>
          <Badge variant="outline">{darkThemes.length}</Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {darkThemes.map((themeData) => (
            <ThemePreviewCard
              key={themeData.id}
              themeId={themeData.id}
              themeName={themeData.name}
              themeDescription={themeData.description}
              themeMode={themeData.mode}
              colors={{
                background: themeData.colors.background,
                text: themeData.colors.text,
                accent: themeData.colors.accent,
                card: themeData.colors.window
              }}
              isActive={isThemeActive(themeData.id)}
              onSelect={() => {
                switchToTheme(themeData.id);
                updateTheme({ currentTheme: themeData.id, themeMode: 'dark' });
              }}
            />
          ))}
        </div>
      </div>

      {/* Custom Theme Creator */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Create Custom Theme
          </CardTitle>
          <CardDescription>
            Design your own theme by customizing colors and appearance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
              <Edit className="w-8 h-8 text-primary" />
            </div>
            <h4 className="font-semibold mb-2">Custom Themes Coming Soon</h4>
            <p className="text-sm text-muted-foreground mb-4">
              We're working on a powerful theme editor that will let you create and save your own custom themes.
            </p>
            <Badge variant="outline" className="text-xs">
              In Development
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Quickly switch between popular theme combinations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchToTheme('writers-sanctuary-light');
                updateTheme({ currentTheme: 'writers-sanctuary-light', themeMode: 'light' });
              }}
            >
              <Sun className="w-3 w-3 mr-1" />
              Default Light
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchToTheme('evening-study-dark');
                updateTheme({ currentTheme: 'evening-study-dark', themeMode: 'dark' });
              }}
            >
              <Moon className="w-3 w-3 mr-1" />
              Default Dark
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switchToTheme('system');
                updateTheme({ themeMode: 'system' });
              }}
            >
              <Monitor className="w-3 w-3 mr-1" />
              Auto
            </Button>
          </div>
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="typography" className="space-y-6 mt-6">
          {/* Text Size */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Type className="w-5 h-5" />
                Text Size
              </CardTitle>
              <CardDescription>
                Adjust the size of text throughout the application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <RadioGroup
                value={typography.textSize}
                onValueChange={(value) => updateTypography({ textSize: value as any })}
                className="grid grid-cols-2 md:grid-cols-3 gap-4"
              >
                {textSizeOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.value} id={option.value} />
                    <Label htmlFor={option.value} className="cursor-pointer flex-1">
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>

              {typography.textSize === 'custom' && (
                <div className="space-y-3 p-4 border rounded-lg bg-muted/30">
                  <Label className="text-sm font-medium">Custom Size: {customSize}px</Label>
                  <Slider
                    value={[customSize]}
                    onValueChange={handleCustomSizeChange}
                    min={10}
                    max={24}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>10px</span>
                    <span>24px</span>
                  </div>
                </div>
              )}

              {/* Preview */}
              <div className="p-4 border rounded-lg bg-card">
                <div className="text-xs text-muted-foreground mb-2">Preview:</div>
                <div
                  className="space-y-2"
                  style={{
                    fontSize: typography.textSize === 'custom'
                      ? `${customSize}px`
                      : textSizeMap[typography.textSize]?.ui || '14px'
                  }}
                >
                  <div>This is how your UI text will appear.</div>
                  <div className="text-muted-foreground">
                    Secondary text and descriptions will look like this.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Font Families */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Font Families</CardTitle>
              <CardDescription>
                Choose fonts for different contexts in your writing environment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Editor Font */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Editor Font</Label>
                  <Select
                    value={typography.editorFont}
                    onValueChange={(value) => updateTypography({ editorFont: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jetbrains-mono">JetBrains Mono</SelectItem>
                      <SelectItem value="fira-code">Fira Code</SelectItem>
                      <SelectItem value="source-code-pro">Source Code Pro</SelectItem>
                      <SelectItem value="consolas">Consolas</SelectItem>
                      <SelectItem value="monaco">Monaco</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* UI Font */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Interface Font</Label>
                  <Select
                    value={typography.uiFont}
                    onValueChange={(value) => updateTypography({ uiFont: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="inter">Inter</SelectItem>
                      <SelectItem value="roboto">Roboto</SelectItem>
                      <SelectItem value="open-sans">Open Sans</SelectItem>
                      <SelectItem value="system-ui">System UI</SelectItem>
                      <SelectItem value="segoe-ui">Segoe UI</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Reading Font */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Reading Font</Label>
                  <Select
                    value={typography.readingFont}
                    onValueChange={(value) => updateTypography({ readingFont: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="crimson-text">Crimson Text</SelectItem>
                      <SelectItem value="georgia">Georgia</SelectItem>
                      <SelectItem value="times-new-roman">Times New Roman</SelectItem>
                      <SelectItem value="libre-baskerville">Libre Baskerville</SelectItem>
                      <SelectItem value="merriweather">Merriweather</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Font Preview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="p-3 border rounded bg-card">
                  <div className="text-xs text-muted-foreground mb-1">Editor:</div>
                  <div
                    className="font-mono text-sm"
                    style={{ fontFamily: fontFamilyMap[typography.editorFont] }}
                  >
                    function write() {'{'}
                    <br />
                    &nbsp;&nbsp;return "Hello";
                    <br />
                    {'}'}
                  </div>
                </div>

                <div className="p-3 border rounded bg-card">
                  <div className="text-xs text-muted-foreground mb-1">Interface:</div>
                  <div
                    className="text-sm"
                    style={{ fontFamily: fontFamilyMap[typography.uiFont] }}
                  >
                    <div className="font-semibold">Menu Item</div>
                    <div>Settings and options</div>
                  </div>
                </div>

                <div className="p-3 border rounded bg-card">
                  <div className="text-xs text-muted-foreground mb-1">Reading:</div>
                  <div
                    className="text-sm"
                    style={{ fontFamily: fontFamilyMap[typography.readingFont] }}
                  >
                    <div className="font-semibold">Chapter One</div>
                    <div>Once upon a time...</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
