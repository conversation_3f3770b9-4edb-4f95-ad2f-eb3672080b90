'use client'

import { useState, useEffect } from 'react'
import { useEditorStore } from '@/stores/editor-store'
import { LazyMonacoEditor } from '@/components/editor/lazy-monaco-editor'
import { EnhancedChapterNavigator } from '@/components/editor/enhanced-chapter-navigator'
import { EnhancedSidebarPanel } from '@/components/editor/enhanced-sidebar-panel'
import { SaveStatusIndicator, useOnlineStatus } from '@/components/version-history/save-status-indicator'
import { PanelManager, PanelConfig, LayoutPreset } from '@/components/editor/panel-manager'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/hooks/use-toast'
import { showDemoWarning } from '@/lib/config/client-config'
import { 
  Save, 
  FileText,
  ArrowLeft,
  BarChart3,
  Brain,
  Feather,
  Upload
} from 'lucide-react'
import Link from 'next/link'

// Demo data
const demoChapters = [
  { id: 'ch1', number: 1, title: 'The Awakening', status: 'completed', wordCount: 2500 },
  { id: 'ch2', number: 2, title: 'Shadows and Whispers', status: 'writing', wordCount: 1200 },
  { id: 'ch3', number: 3, title: 'The Ancient Oak', status: 'planned', wordCount: 0 },
  { id: 'ch4', number: 4, title: 'Secrets Unveiled', status: 'planned', wordCount: 0 },
  { id: 'ch5', number: 5, title: 'The Prophecy', status: 'planned', wordCount: 0 }
]

const sampleContent = `Chapter 1: The Awakening

The old oak tree stood sentinel at the edge of Willowbrook, its gnarled branches reaching toward the storm-darkened sky. Sarah approached cautiously, her heart pounding as she remembered her grandmother's warnings about the ancient tree and its mysterious powers.

"Never go near the oak when the moon is full," her grandmother had whispered on her deathbed. "The spirits that dwell within are restless, and they hunger for the living."

But Sarah was no longer the frightened child who had cowered beneath her grandmother's tales. She was a scholar now, armed with knowledge and determination. The leather-bound journal in her hands contained decades of research about the supernatural phenomena that plagued their small town.

As she drew closer to the massive trunk, the wind began to howl through the branches, creating an otherworldly symphony that seemed to call her name. The bark felt warm beneath her palm, pulsing with an energy that made her fingertips tingle.

Suddenly, the world around her began to shift and blur. The familiar landscape of Willowbrook dissolved into something far more ancient and mysterious. She found herself standing in a moonlit grove where the very air shimmered with magic.

"You have come at last," a voice whispered from the shadows between the trees. Sarah spun around, searching for the source, but saw only dancing shadows and silver light filtering through leaves that seemed to glow with their own inner fire.

The journal in her hands grew warm, its pages fluttering open of their own accord. Words began to appear on the yellowed parchment, written in a script she had never seen before but somehow understood perfectly.

"The time of choosing has arrived. Will you embrace your heritage, or will you turn away as your mother did before you?"

Sarah's breath caught in her throat. Her mother had never spoken of any heritage beyond their simple family history. But as she stared at the mysterious words, memories began to surface—fragments of conversations overheard in childhood, strange dreams that had haunted her sleep, and the inexplicable pull she had always felt toward the old oak.

The wind picked up, swirling around her with increasing intensity. The journal's pages turned rapidly, revealing more secrets with each flutter. Sarah realized she stood at a crossroads, not just in this mystical grove, but in her very existence.

Whatever choice she made in this moment would change everything.`

export default function KnowledgeDemoPage() {
  const [showRightPanel, setShowRightPanel] = useState(true)
  const [rightPanelWidth, setRightPanelWidth] = useState(440)
  const [rightPanelTab, setRightPanelTab] = useState('knowledge')
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [showWritingStats, setShowWritingStats] = useState(false)
  const [currentChapterId, setCurrentChapterId] = useState('ch1')
  const [content, setContent] = useState(sampleContent)
  
  const {
    selectedText,
    showChapterNavigator,
    toggleChapterNavigator,
    updateChapterList
  } = useEditorStore()
  
  const isOnline = useOnlineStatus()
  
  // Panel configuration
  const [panelConfigs, setPanelConfigs] = useState<PanelConfig[]>([
    { id: 'chapters', label: 'Chapters', icon: FileText, visible: true, pinned: false, position: 'left' },
    { id: 'right-panel', label: 'Project Tools', icon: Brain, visible: showRightPanel, pinned: false, position: 'right' },
    { id: 'stats', label: 'Writing Stats', icon: BarChart3, visible: showWritingStats, pinned: false, position: 'bottom' }
  ])
  
  // Initialize chapters in store and show demo warning
  useEffect(() => {
    updateChapterList(demoChapters)
    showDemoWarning()
  }, [updateChapterList])
  
  // Panel management functions
  const handlePanelToggle = (panelId: string) => {
    switch (panelId) {
      case 'chapters':
        toggleChapterNavigator()
        break
      case 'right-panel':
        setShowRightPanel(!showRightPanel)
        break
      case 'stats':
        setShowWritingStats(!showWritingStats)
        break
    }
  }
  
  const handlePanelPin = (panelId: string) => {
    setPanelConfigs(configs => 
      configs.map(config => 
        config.id === panelId ? { ...config, pinned: !config.pinned } : config
      )
    )
  }
  
  const handleLayoutChange = (newPanels: PanelConfig[]) => {
    setPanelConfigs(newPanels)
    // Apply the layout changes
    newPanels.forEach(panel => {
      const currentPanel = panelConfigs.find(p => p.id === panel.id)
      if (currentPanel && currentPanel.visible !== panel.visible) {
        handlePanelToggle(panel.id)
      }
    })
  }
  
  const handlePresetSelect = (preset: LayoutPreset) => {
    setPanelConfigs(preset.panels)
  }
  
  const handleRightPanelTabChange = (tab: string) => {
    setRightPanelTab(tab)
  }
  
  // Update panel configs when visibility changes
  useEffect(() => {
    setPanelConfigs(configs => configs.map(config => {
      switch (config.id) {
        case 'chapters': return { ...config, visible: showChapterNavigator }
        case 'right-panel': return { ...config, visible: showRightPanel }
        case 'stats': return { ...config, visible: showWritingStats }
        default: return config
      }
    }))
  }, [showChapterNavigator, showRightPanel, showWritingStats])
  
  // Demo save function
  const saveChapter = () => {
    setIsSaving(true)
    setTimeout(() => {
      setIsSaving(false)
      setLastSaved(new Date())
      setHasUnsavedChanges(false)
      toast({
        title: "Chapter saved",
        description: "This is a demo - no actual saving occurs"
      })
    }, 1000)
  }
  
  // Track content changes
  useEffect(() => {
    if (content !== sampleContent) {
      setHasUnsavedChanges(true)
    }
  }, [content])
  
  const handleChapterSelect = (id: string) => {
    setCurrentChapterId(id)
    // In demo, just show the same content
    toast({
      title: "Chapter selected",
      description: `Switched to ${demoChapters.find(ch => ch.id === id)?.title}`
    })
  }
  
  const handleCreateChapter = () => {
    toast({
      title: "Create chapter",
      description: "This is a demo - chapters cannot be created"
    })
  }
  
  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="border-b px-4 py-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <Feather className="h-5 w-5 text-primary" />
              <h1 className="text-lg font-semibold text-foreground">
                Knowledge Panel Demo
              </h1>
            </div>
            <Badge variant="outline" className="ml-2">
              Interactive Demo
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <SaveStatusIndicator 
            isSaving={isSaving}
            lastSaved={lastSaved}
            hasUnsavedChanges={hasUnsavedChanges}
            isOnline={isOnline}
            onManualSave={saveChapter}
          />
          
          <Button variant="ghost" size="sm" disabled>
            <Upload className="h-4 w-4" />
          </Button>
          
          <PanelManager
            currentPanels={panelConfigs}
            onPanelToggle={handlePanelToggle}
            onPanelPin={handlePanelPin}
            onLayoutChange={handleLayoutChange}
            onPresetSelect={handlePresetSelect}
          />
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowRightPanel(!showRightPanel)}
            className={showRightPanel ? 'bg-primary/10 text-primary' : ''}
          >
            <Brain className="h-4 w-4" />
          </Button>

          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowWritingStats(!showWritingStats)}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>
          
          <Button 
            onClick={saveChapter}
            disabled={isSaving}
            size="sm"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Chapter Navigator */}
        {showChapterNavigator && (
          <div className="border-r">
            <EnhancedChapterNavigator
              projectId="demo-project"
              currentChapterId={currentChapterId}
              content={content}
              onChapterSelect={handleChapterSelect}
              onCreateChapter={handleCreateChapter}
            />
          </div>
        )}

        {/* Main Editor Area */}
        <div className="flex-1 flex">
          {/* Editor */}
          <div className="flex-1">
            <LazyMonacoEditor
              initialContent={content}
              onContentChange={setContent}
              onSave={saveChapter}
              showToolbar={true}
              showStats={showWritingStats}
              showAISuggestions={true}
            />
          </div>

          {/* Enhanced Right Panel */}
          {showRightPanel && (
            <EnhancedSidebarPanel
              projectId="demo-project"
              chapterId={currentChapterId}
              selectedText={selectedText}
              userId="demo-user"
              onClose={() => setShowRightPanel(false)}
              initialWidth={rightPanelWidth}
              onWidthChange={setRightPanelWidth}
              defaultTab={rightPanelTab}
              onTabChange={handleRightPanelTabChange}
            />
          )}
        </div>
      </div>

      {/* Instructions Overlay */}
      <div className="fixed bottom-6 left-6 max-w-md z-50">
        <div className="bg-background/95 backdrop-blur-sm rounded-lg shadow-lg border border-border p-4">
          <h3 className="font-semibold text-foreground mb-2">
            Knowledge Panel Demo Instructions:
          </h3>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Select text in the editor to capture it in the Knowledge panel</li>
            <li>• Use the right panel tabs to switch between Knowledge, AI Chat, Story Bible, and more</li>
            <li>• Toggle panels using the layout manager in the header</li>
            <li>• Resize panels by dragging their edges</li>
            <li>• Try different layout presets from the Layout dropdown</li>
            <li>• This is a fully interactive demo with the complete editor interface</li>
          </ul>
        </div>
      </div>
    </div>
  )
}