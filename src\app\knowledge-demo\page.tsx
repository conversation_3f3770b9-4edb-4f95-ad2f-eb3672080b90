'use client'

import { useState } from 'react'
import { KnowledgeInputPanel } from '@/components/editor/knowledge-input-panel'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, BookOpen } from 'lucide-react'

export default function KnowledgeDemoPage() {
  const [selectedText, setSelectedText] = useState<string>('')
  const [showPanel, setShowPanel] = useState(true)

  const sampleText = `// React Component Implementation
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  preferences: {
    theme: 'light' | 'dark';
    notifications: boolean;
  };
}

export function ProfileManager() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchUserProfile() {
      try {
        const response = await fetch('/api/user/profile');
        if (!response.ok) {
          throw new Error('Failed to fetch user profile');
        }
        const userData = await response.json();
        setUser(userData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    }

    fetchUserProfile();
  }, []);

  const handleThemeToggle = async () => {
    if (!user) return;

    const newTheme = user.preferences.theme === 'light' ? 'dark' : 'light';

    try {
      await fetch('/api/user/preferences', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ theme: newTheme })
      });

      setUser(prev => prev ? {
        ...prev,
        preferences: { ...prev.preferences, theme: newTheme }
      } : null);
    } catch (err) {
      console.error('Failed to update theme:', err);
    }
  };

  if (loading) return <div>Loading user profile...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!user) return <div>No user data available</div>;

  return (
    <div className="profile-manager">
      <h2>Welcome, {user.name}</h2>
      <p>Email: {user.email}</p>
      <Button onClick={handleThemeToggle}>
        Switch to {user.preferences.theme === 'light' ? 'dark' : 'light'} theme
      </Button>
    </div>
  );
}`

  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString().trim())
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 via-amber-50/50 to-orange-50/30 paper-texture">
      {/* Header */}
      <header className="border-b border-warm-200/50 bg-white/80 backdrop-blur-xl shadow-sm">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-literary-amber to-warm-600 blur-sm opacity-20" />
              <div className="relative w-8 h-8 bg-gradient-to-br from-literary-amber to-warm-600 rounded-lg flex items-center justify-center shadow-md">
                <Feather className="w-5 h-5 text-white" />
              </div>
            </div>
            <h1 className="text-2xl font-literary font-bold text-warm-800">
              Knowledge Input Panel Demo
            </h1>
          </div>
          
          <Button 
            onClick={() => setShowPanel(!showPanel)}
            variant={showPanel ? "literary" : "outline"}
          >
            <BookOpen className="w-4 h-4 mr-2" />
            {showPanel ? 'Hide Panel' : 'Show Panel'}
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sample Text Editor */}
        <div className={`flex-1 p-6 ${showPanel ? 'mr-4' : ''}`}>
          <Card className="h-full border-warm-200 bg-white/90 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <CardTitle className="font-mono text-gray-800 dark:text-gray-200">
                // Sample React Component
              </CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
                Select any code below to see it appear in the Knowledge Input Panel
              </p>
            </CardHeader>
            <CardContent className="flex-1">
              <div 
                className="h-full p-6 bg-warm-50/30 rounded-xl border border-warm-200/50 overflow-y-auto"
                onMouseUp={handleTextSelection}
              >
                <pre className="text-sm font-mono text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap">
                  {sampleText}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Knowledge Input Panel */}
        {showPanel && (
          <div className="w-96 border-l border-warm-200/50 bg-warm-50/30">
            <KnowledgeInputPanel
              projectId="demo-project"
              chapterId="demo-chapter"
              selectedText={selectedText}
              onClose={() => setShowPanel(false)}
            />
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="fixed bottom-6 left-6 max-w-md">
        <Card className="border-literary-amber/30 bg-literary-gold/10 backdrop-blur-sm">
          <CardContent className="p-4">
            <h3 className="font-mono font-semibold text-gray-800 dark:text-gray-200 mb-2">
              // Usage Instructions:
            </h3>
            <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1 font-mono">
              <li>• Select code to see it in the panel</li>
              <li>• Switch between AI Assistant and Knowledge Base</li>
              <li>• Add components, functions, and documentation</li>
              <li>• Ask AI for code analysis and suggestions</li>
              <li>• Drag panel edge to resize width</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
