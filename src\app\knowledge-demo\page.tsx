'use client'

import { useState } from 'react'
import { KnowledgeInputPanel } from '@/components/editor/knowledge-input-panel'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>ather, BookOpen } from 'lucide-react'

export default function KnowledgeDemoPage() {
  const [selectedText, setSelectedText] = useState<string>('')
  const [showPanel, setShowPanel] = useState(true)

  const sampleText = `Chapter 1: The Awakening

The old oak tree stood sentinel at the edge of Willowbrook, its gnarled branches reaching toward the storm-darkened sky. <PERSON> approached cautiously, her heart pounding as she remembered her grandmother's warnings about the ancient tree and its mysterious powers.

"Never go near the oak when the moon is full," her grandmother had whispered on her deathbed. "The spirits that dwell within are restless, and they hunger for the living."

But <PERSON> was no longer the frightened child who had cowered beneath her grandmother's tales. She was a scholar now, armed with knowledge and determination. The leather-bound journal in her hands contained decades of research about the supernatural phenomena that plagued their small town.

As she drew closer to the massive trunk, the wind began to howl through the branches, creating an otherworldly symphony that seemed to call her name. The bark felt warm beneath her palm, pulsing with an energy that made her fingertips tingle.

Suddenly, the world around her began to shift and blur. The familiar landscape of <PERSON>brook dissolved into something far more ancient and mysterious. She found herself standing in a moonlit grove where the very air shimmered with magic.

"You have come at last," a voice whispered from the shadows between the trees. <PERSON> spun around, searching for the source, but saw only dancing shadows and silver light filtering through leaves that seemed to glow with their own inner fire.

The journal in her hands grew warm, its pages fluttering open of their own accord. Words began to appear on the yellowed parchment, written in a script she had never seen before but somehow understood perfectly.

"The time of choosing has arrived. Will you embrace your heritage, or will you turn away as your mother did before you?"

Sarah's breath caught in her throat. Her mother had never spoken of any heritage beyond their simple family history. But as she stared at the mysterious words, memories began to surface—fragments of conversations overheard in childhood, strange dreams that had haunted her sleep, and the inexplicable pull she had always felt toward the old oak.

The wind picked up, swirling around her with increasing intensity. The journal's pages turned rapidly, revealing more secrets with each flutter. Sarah realized she stood at a crossroads, not just in this mystical grove, but in her very existence.

Whatever choice she made in this moment would change everything.`

  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString().trim())
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 via-amber-50/50 to-orange-50/30 paper-texture">
      {/* Header */}
      <header className="border-b border-warm-200/50 bg-white/80 backdrop-blur-xl shadow-sm">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-literary-amber to-warm-600 blur-sm opacity-20" />
              <div className="relative w-8 h-8 bg-gradient-to-br from-literary-amber to-warm-600 rounded-lg flex items-center justify-center shadow-md">
                <Feather className="w-5 h-5 text-white" />
              </div>
            </div>
            <h1 className="text-2xl font-literary font-bold text-warm-800">
              Knowledge Input Panel Demo
            </h1>
          </div>
          
          <Button 
            onClick={() => setShowPanel(!showPanel)}
            variant={showPanel ? "literary" : "outline"}
          >
            <BookOpen className="w-4 h-4 mr-2" />
            {showPanel ? 'Hide Panel' : 'Show Panel'}
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sample Text Editor */}
        <div className={`flex-1 p-6 ${showPanel ? 'mr-4' : ''}`}>
          <Card className="h-full border-warm-200 bg-white/90 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <CardTitle className="font-mono text-gray-800 dark:text-gray-200">
                Sample Story Text
              </CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
                Select any text below to see it appear in the Knowledge Input Panel
              </p>
            </CardHeader>
            <CardContent className="flex-1">
              <div 
                className="h-full p-6 bg-warm-50/30 rounded-xl border border-warm-200/50 overflow-y-auto"
                onMouseUp={handleTextSelection}
              >
                <pre className="text-sm font-mono text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap">
                  {sampleText}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Knowledge Input Panel */}
        {showPanel && (
          <div className="w-96 border-l border-warm-200/50 bg-warm-50/30">
            <KnowledgeInputPanel
              projectId="demo-project"
              chapterId="demo-chapter"
              selectedText={selectedText}
              onClose={() => setShowPanel(false)}
            />
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="fixed bottom-6 left-6 max-w-md">
        <Card className="border-literary-amber/30 bg-literary-gold/10 backdrop-blur-sm">
          <CardContent className="p-4">
            <h3 className="font-mono font-semibold text-gray-800 dark:text-gray-200 mb-2">
              Usage Instructions:
            </h3>
            <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1 font-mono">
              <li>• Select text from the story to see it in the panel</li>
              <li>• Switch between AI Assistant and Knowledge Base</li>
              <li>• Add characters, locations, and story notes</li>
              <li>• Ask AI for writing suggestions and analysis</li>
              <li>• Drag panel edge to resize width</li>
              <li>• Use Initialize button to generate story elements</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
