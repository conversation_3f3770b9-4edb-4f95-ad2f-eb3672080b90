# Demo Environment Variables
# These are dummy values for running the demo locally

# Supabase Configuration (dummy values)
NEXT_PUBLIC_SUPABASE_URL=https://dummy.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=dummy_anon_key_for_demo_purposes_only
SUPABASE_SERVICE_ROLE_KEY=dummy_service_role_key_for_demo_purposes_only

# OpenAI Configuration (dummy value - AI features won't work)
OPENAI_API_KEY=dummy_openai_key_for_demo_purposes_only

# Stripe Configuration (dummy values - payments won't work)
STRIPE_SECRET_KEY=dummy_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=dummy_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=dummy_stripe_webhook_secret

# Stripe Price IDs (dummy values)
STRIPE_PRICE_ID_BASIC=price_dummy_basic
STRIPE_PRICE_ID_PRO=price_dummy_pro
STRIPE_PRICE_ID_ENTERPRISE=price_dummy_enterprise

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Development Configuration
NEXT_PUBLIC_DEV_BYPASS_AUTH=true
DEV_USER_EMAIL=<EMAIL>
DEV_USER_ID=demo-user-123

# Admin Configuration
ADMIN_EMAILS=<EMAIL>