import { loader } from '@monaco-editor/react';

// Configure Monaco to load from local node_modules instead of CDN
export function configureMonacoLoader() {
  // Only configure in browser environment
  if (typeof window === 'undefined') return;

  // Prevent loading from CDN entirely
  loader.config({ 
    monaco: undefined 
  });
  
  // Import monaco-editor directly
  loader.init().then((monaco) => {
    console.log('Monaco loaded locally:', monaco);
  }).catch((error) => {
    console.error('Failed to load Monaco locally:', error);
    // Fallback to a different CDN if local loading fails
    loader.config({
      paths: {
        vs: 'https://unpkg.com/monaco-editor@0.52.2/min/vs'
      }
    });
  });
}