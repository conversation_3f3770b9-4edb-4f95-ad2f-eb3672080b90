'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { toast } from '@/hooks/use-toast'
import { Upload } from 'lucide-react'

interface BookImportDialogSimpleProps {
  projectId: string
  onImportComplete?: () => void
  trigger?: React.ReactNode
}

export function BookImportDialogSimple({ projectId, onImportComplete, trigger }: BookImportDialogSimpleProps) {
  const handleImport = () => {
    toast({
      title: "Import functionality",
      description: "Book import is not available in demo mode"
    })
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Import Book
          </Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Import Book</DialogTitle>
          <DialogDescription>
            Import functionality is not available in demo mode. 
            In the full version, you can import Word (.docx), EPUB, and PDF files.
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end mt-4">
          <Button onClick={handleImport} variant="outline">
            OK
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}