'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export default function KnowledgeDemoV2Page() {
  const [step, setStep] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const components = [
    {
      name: 'Basic Layout',
      render: () => (
        <div className="h-screen flex flex-col bg-background">
          <header className="border-b px-4 py-2 flex items-center justify-between">
            <h1 className="text-lg font-semibold">Knowledge Demo V2 - Step 1</h1>
          </header>
          <div className="flex-1 flex overflow-hidden">
            <div className="w-80 border-r p-4">Left Panel</div>
            <div className="flex-1 p-4">Center Content</div>
            <div className="w-96 border-l p-4">Right Panel</div>
          </div>
        </div>
      )
    },
    {
      name: 'With Editor Store',
      render: () => {
        const { useEditorStore } = require('@/stores/editor-store')
        const store = useEditorStore()
        
        // Initialize demo chapters
        useEffect(() => {
          store.updateChapterList([
            { id: 'ch1', number: 1, title: 'Chapter 1', status: 'completed', wordCount: 1000 }
          ])
        }, [])

        return (
          <div className="h-screen flex flex-col bg-background">
            <header className="border-b px-4 py-2 flex items-center justify-between">
              <h1 className="text-lg font-semibold">Knowledge Demo V2 - Step 2</h1>
              <Badge>Chapters: {store.chapters.length}</Badge>
            </header>
            <div className="flex-1 flex overflow-hidden">
              <div className="w-80 border-r p-4">Left Panel</div>
              <div className="flex-1 p-4">Center Content</div>
              <div className="w-96 border-l p-4">Right Panel</div>
            </div>
          </div>
        )
      }
    },
    {
      name: 'With Simple Navigator',
      render: () => {
        const { useEditorStore } = require('@/stores/editor-store')
        const { SimpleChapterNavigator } = require('@/components/editor/simple-chapter-navigator')
        const store = useEditorStore()
        const [currentChapterId, setCurrentChapterId] = useState('ch1')

        useEffect(() => {
          store.updateChapterList([
            { id: 'ch1', number: 1, title: 'Chapter 1', status: 'completed', wordCount: 1000 },
            { id: 'ch2', number: 2, title: 'Chapter 2', status: 'writing', wordCount: 500 }
          ])
        }, [])

        return (
          <div className="h-screen flex flex-col bg-background">
            <header className="border-b px-4 py-2 flex items-center justify-between">
              <h1 className="text-lg font-semibold">Knowledge Demo V2 - Step 3</h1>
            </header>
            <div className="flex-1 flex overflow-hidden">
              {store.showChapterNavigator && (
                <SimpleChapterNavigator
                  projectId="demo"
                  currentChapterId={currentChapterId}
                  onChapterSelect={(id) => setCurrentChapterId(id)}
                  onCreateChapter={() => console.log('Create chapter')}
                />
              )}
              <div className="flex-1 p-4">Center Content</div>
              <div className="w-96 border-l p-4">Right Panel</div>
            </div>
          </div>
        )
      }
    },
    {
      name: 'With Text Editor',
      render: () => {
        const { useEditorStore } = require('@/stores/editor-store')
        const store = useEditorStore()
        const [content, setContent] = useState('Sample content')

        return (
          <div className="h-screen flex flex-col bg-background">
            <header className="border-b px-4 py-2 flex items-center justify-between">
              <h1 className="text-lg font-semibold">Knowledge Demo V2 - Step 4</h1>
            </header>
            <div className="flex-1 flex overflow-hidden">
              <div className="w-80 border-r p-4">Left Panel</div>
              <div className="flex-1 p-4">
                <textarea 
                  className="w-full h-full p-4 border rounded resize-none"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                />
              </div>
              <div className="w-96 border-l p-4">
                <h2 className="font-semibold mb-2">Selected Text:</h2>
                <p className="text-sm text-muted-foreground">
                  {store.selectedText || 'Select text in the editor'}
                </p>
              </div>
            </div>
          </div>
        )
      }
    }
  ]

  const renderStep = () => {
    if (step >= components.length) {
      return (
        <div className="p-8">
          <h1 className="text-2xl font-bold mb-4">All Steps Complete!</h1>
          <p>All components loaded successfully. The issue might be with:</p>
          <ul className="list-disc ml-6 mt-2">
            <li>Monaco Editor</li>
            <li>Enhanced Sidebar Panel</li>
            <li>Panel Manager</li>
          </ul>
        </div>
      )
    }

    try {
      setError(null)
      return components[step].render()
    } catch (e) {
      setError(e.message)
      return (
        <div className="p-8">
          <h1 className="text-xl font-bold text-red-600 mb-2">Error in {components[step].name}</h1>
          <p className="text-red-500">{e.message}</p>
          <Button onClick={() => setStep(0)} className="mt-4">Start Over</Button>
        </div>
      )
    }
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-xl font-bold text-red-600 mb-2">Error</h1>
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={() => { setError(null); setStep(0); }}>Start Over</Button>
      </div>
    )
  }

  return (
    <>
      {renderStep()}
      {step < components.length && (
        <div className="fixed bottom-4 right-4">
          <Button onClick={() => setStep(step + 1)}>
            Next Step ({step + 1}/{components.length})
          </Button>
        </div>
      )}
    </>
  )
}