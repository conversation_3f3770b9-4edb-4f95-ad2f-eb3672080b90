"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  Users,
  BookOpen,
  PenTool,
  Zap,
  CheckCircle,
  Clock,
  ArrowRight,
  Play,
  Pause,
  RotateCcw
} from "lucide-react";

interface Agent {
  id: string;
  name: string;
  role: string;
  description: string;
  icon: React.ComponentType<any>;
  status: 'idle' | 'working' | 'complete' | 'waiting';
  progress: number;
  output?: string;
  color: string;
}

const agents: Agent[] = [
  {
    id: 'story-architect',
    name: 'Story Architect',
    role: 'Structure Planning',
    description: 'Creates the overall story structure, plot points, and narrative arc',
    icon: BookOpen,
    status: 'idle',
    progress: 0,
    color: 'blue'
  },
  {
    id: 'character-developer',
    name: 'Character Developer',
    role: 'Character Creation',
    description: '<PERSON>elops detailed character profiles, relationships, and arcs',
    icon: Users,
    status: 'idle',
    progress: 0,
    color: 'purple'
  },
  {
    id: 'world-builder',
    name: 'World Builder',
    role: 'Setting & Lore',
    description: 'Constructs the world, locations, cultures, and magical systems',
    icon: Brain,
    status: 'idle',
    progress: 0,
    color: 'green'
  },
  {
    id: 'chapter-planner',
    name: 'Chapter Planner',
    role: 'Chapter Outlines',
    description: 'Creates detailed chapter-by-chapter outlines and scene breakdowns',
    icon: PenTool,
    status: 'idle',
    progress: 0,
    color: 'amber'
  }
];

const agentOutputs = {
  'story-architect': {
    title: 'Three-Act Structure Generated',
    content: `Act I: The Awakening (Chapters 1-8)
• Aria discovers her connection to the Heartstone
• Introduction to Aethermoor Academy and key characters
• First manifestation of ancient powers
• Inciting incident: The crystal's activation

Act II: The Journey (Chapters 9-18)
• Training and power development
• Discovery of the ancient prophecy
• Introduction of the antagonist Zara Nightwhisper
• Rising conflict and character relationships

Act III: The Convergence (Chapters 19-25)
• Final confrontation with dark forces
• Aria's transformation into the Crystal Guardian
• Resolution of character arcs
• New equilibrium and series setup`
  },
  'character-developer': {
    title: 'Character Profiles Created',
    content: `Aria Stormwind (Protagonist)
• Age: 17, Crystal Mage in training
• Arc: Reluctant hero → Confident guardian
• Key relationships: Marcus (mentor), Zara (rival)
• Internal conflict: Power vs. responsibility

Marcus Ironforge (Mentor)
• Age: 156, Dwarf master of earth magic
• Arc: Protective teacher → Trusting guide
• Backstory: Former Crystal Guardian
• Key trait: Wisdom through experience

Zara Nightwhisper (Antagonist)
• Age: 19, Shadow magic prodigy
• Arc: Ambitious student → Corrupted villain
• Motivation: Believes power should rule
• Connection: Former friend of Aria`
  },
  'world-builder': {
    title: 'World Systems Established',
    content: `Aethermoor Realm
• Geography: Floating crystal islands connected by energy bridges
• Magic System: Crystal-based elemental magic
• Cultures: Five crystal clans (Fire, Water, Earth, Air, Spirit)
• History: Ancient Crystal War 1000 years ago

Key Locations:
• Aethermoor Academy: Central training ground
• Crystal Caverns: Source of all magical power
• Shadowlands: Corrupted realm beyond the barriers
• The Nexus: Where all crystal energies converge

Magic Rules:
• Power drawn from crystal resonance
• Emotional state affects magical strength
• Overuse leads to crystal corruption`
  },
  'chapter-planner': {
    title: 'Chapter Outlines Complete',
    content: `Chapter 1: The Crystal's Call (3,500 words)
• Scene 1: Aria at the Heartstone
• Scene 2: Crystal activation and vision
• Scene 3: Marcus explains the prophecy
• Cliffhanger: Dark energy detected

Chapter 2: Shadows Awakening (3,200 words)
• Scene 1: Academy in chaos
• Scene 2: Zara's first appearance
• Scene 3: Aria's power training begins
• Hook: Ancient enemy stirs

Chapter 3: The First Trial (3,800 words)
• Scene 1: Magical aptitude test
• Scene 2: Aria's unexpected results
• Scene 3: Confrontation with Zara
• Revelation: Aria's true heritage`
  }
};

export function DemoAgents() {
  const [agentStates, setAgentStates] = useState(agents);
  const [isRunning, setIsRunning] = useState(false);
  const [currentAgentIndex, setCurrentAgentIndex] = useState(0);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);

  useEffect(() => {
    if (isRunning && currentAgentIndex < agents.length) {
      const interval = setInterval(() => {
        setAgentStates(prev => {
          const newStates = [...prev];
          const currentAgent = newStates[currentAgentIndex];
          
          if (currentAgent.progress < 100) {
            currentAgent.progress += 2;
            currentAgent.status = 'working';
          } else {
            currentAgent.status = 'complete';
            if (currentAgentIndex < agents.length - 1) {
              setCurrentAgentIndex(currentAgentIndex + 1);
            } else {
              setIsRunning(false);
            }
          }
          
          return newStates;
        });
      }, 50);

      return () => clearInterval(interval);
    }
  }, [isRunning, currentAgentIndex]);

  const startDemo = () => {
    setAgentStates(agents.map(agent => ({ ...agent, status: 'idle', progress: 0 })));
    setCurrentAgentIndex(0);
    setIsRunning(true);
  };

  const resetDemo = () => {
    setIsRunning(false);
    setCurrentAgentIndex(0);
    setAgentStates(agents.map(agent => ({ ...agent, status: 'idle', progress: 0 })));
    setSelectedAgent(null);
  };

  const getStatusColor = (status: string, color: string) => {
    switch (status) {
      case 'working': return 'text-primary border-primary/50';
      case 'complete': return 'text-green-500 border-green-500/50';
      case 'waiting': return 'text-blue-500 border-blue-500/50';
      default: return 'text-muted-foreground border-border';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'working': return <Clock className="w-4 h-4 animate-spin" />;
      case 'complete': return <CheckCircle className="w-4 h-4" />;
      case 'waiting': return <Clock className="w-4 h-4" />;
      default: return <Brain className="w-4 h-4" />;
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      <Card className="border-border bg-card/50 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl flex items-center gap-2">
              <Brain className="w-6 h-6 text-primary" />
              AI Agent Pipeline
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="border-primary/50 text-primary">
                Multi-Agent System
              </Badge>
              <Button
                onClick={startDemo}
                disabled={isRunning}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
              >
                {isRunning ? (
                  <>
                    <Pause className="w-4 h-4 mr-2" />
                    Running...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Start Pipeline
                  </>
                )}
              </Button>
              <Button
                onClick={resetDemo}
                variant="outline"
                className="border-border text-foreground hover:bg-accent hover:text-accent-foreground"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
          
          <p className="text-muted-foreground mt-2">
            Watch our specialized AI agents work together to create your complete story structure, 
            characters, world, and chapter outlines.
          </p>
        </CardHeader>

        <CardContent>
          <div className="space-y-6">
            {/* Agent Pipeline Visualization */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {agentStates.map((agent, index) => {
                const AgentIcon = agent.icon;
                const isActive = index === currentAgentIndex && isRunning;
                
                return (
                  <Card
                    key={agent.id}
                    className={`cursor-pointer transition-all border ${
                      selectedAgent === agent.id
                        ? 'border-primary/50 bg-primary/10'
                        : agent.status === 'complete'
                        ? 'border-green-500/50 bg-green-500/10'
                        : agent.status === 'working'
                        ? 'border-primary/50 bg-primary/10'
                        : 'border-border bg-card/50 hover:border-accent-foreground/20'
                    }`}
                    onClick={() => setSelectedAgent(selectedAgent === agent.id ? null : agent.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <AgentIcon className={`w-6 h-6 ${
                          agent.status === 'complete' ? 'text-green-500' :
                          agent.status === 'working' ? 'text-primary' :
                          'text-muted-foreground'
                        }`} />
                        <Badge variant="outline" className={getStatusColor(agent.status, agent.color)}>
                          {getStatusIcon(agent.status)}
                          <span className="ml-1 capitalize">{agent.status}</span>
                        </Badge>
                      </div>
                      
                      <h3 className="font-semibold text-sm mb-1">{agent.name}</h3>
                      <p className="text-xs text-muted-foreground mb-3">{agent.description}</p>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="text-foreground">{agent.progress}%</span>
                        </div>
                        <Progress 
                          value={agent.progress} 
                          className={`h-2 ${
                            agent.status === 'complete' ? '[&>div]:bg-green-500' :
                            agent.status === 'working' ? '[&>div]:bg-primary' :
                            '[&>div]:bg-muted-foreground'
                          }`}
                        />
                      </div>
                      
                      {isActive && (
                        <div className="mt-2 flex items-center gap-1 text-xs text-primary">
                          <Zap className="w-3 h-3" />
                          <span>Processing...</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Agent Flow Visualization */}
            <div className="flex items-center justify-center space-x-4 py-4">
              {agentStates.map((agent, index) => (
                <div key={agent.id} className="flex items-center">
                  <div className={`w-3 h-3 rounded-full ${
                    agent.status === 'complete' ? 'bg-green-500' :
                    agent.status === 'working' ? 'bg-primary animate-pulse' :
                    'bg-muted-foreground'
                  }`} />
                  {index < agentStates.length - 1 && (
                    <ArrowRight className={`w-4 h-4 mx-2 ${
                      agentStates[index + 1].status !== 'idle' ? 'text-primary' : 'text-muted-foreground'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            {/* Agent Output Display */}
            {selectedAgent && agentStates.find(a => a.id === selectedAgent)?.status === 'complete' && (
              <Card className="border-border bg-card/50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    {agentOutputs[selectedAgent as keyof typeof agentOutputs]?.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-sm text-muted-foreground whitespace-pre-wrap font-mono">
                    {agentOutputs[selectedAgent as keyof typeof agentOutputs]?.content}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Pipeline Status */}
            <Card className="border-border bg-card">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold">Pipeline Status</h4>
                    <p className="text-sm text-muted-foreground">
                      {isRunning 
                        ? `Processing with ${agentStates[currentAgentIndex]?.name}...`
                        : agentStates.every(a => a.status === 'complete')
                        ? 'All agents completed successfully!'
                        : 'Ready to start processing'
                      }
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">
                      {Math.round(agentStates.reduce((acc, agent) => acc + agent.progress, 0) / agents.length)}%
                    </div>
                    <div className="text-xs text-muted-foreground">Overall Progress</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Information Panel */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="border-blue-500/20 bg-blue-500/10">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Brain className="w-5 h-5 text-blue-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-500">Parallel Processing</h4>
                      <p className="text-sm text-blue-500/80 mt-1">
                        Agents can work simultaneously on different aspects of your story, dramatically reducing generation time.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-purple-500/20 bg-purple-500/10">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Zap className="w-5 h-5 text-purple-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-purple-500">Context Sharing</h4>
                      <p className="text-sm text-purple-500/80 mt-1">
                        Each agent builds upon the work of previous agents, ensuring perfect consistency across all elements.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
