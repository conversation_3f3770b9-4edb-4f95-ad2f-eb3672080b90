'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useResizablePanel } from '@/hooks/use-resizable-panel'
import { KnowledgeGenerator } from './knowledge-generator'
import {
  Send,
  X,
  Bot,
  User,
  Trash2,
  BookOpen,
  Users,
  MapPin,
  Lightbulb,
  Plus,
  Edit3,
  Save,
  Feather,
  Zap,
  Heart,
  Sword,
  Globe,
  Clock,
  Target,
  Palette,
  Settings,
  FileText,
  Layers,
  GitBranch,
  Wand2
} from 'lucide-react'
import ReactMarkdown from 'react-markdown'

interface KnowledgeItem {
  id: string
  type: 'character' | 'location' | 'story-arc' | 'theme' | 'conflict' | 'world-building' | 'timeline' | 'setting' | 'plot-device' | 'research' | 'note'
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  category?: string
  importance?: 'low' | 'medium' | 'high'
  connections?: string[] // IDs of related items
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  context?: {
    selectedText?: string
    knowledgeItems?: string[]
  }
}

interface KnowledgeInputPanelProps {
  projectId: string
  chapterId?: string
  selectedText?: string
  onClose?: () => void
  initialWidth?: number
  onWidthChange?: (width: number) => void
}

export function KnowledgeInputPanel({
  projectId,
  chapterId,
  selectedText,
  onClose,
  initialWidth = 384,
  onWidthChange
}: KnowledgeInputPanelProps) {
  const { width, isResizing, ResizeHandle } = useResizablePanel({
    initialWidth,
    minWidth: 320,
    maxWidth: 800,
    onResize: onWidthChange
  })

  const [activeTab, setActiveTab] = useState<'chat' | 'knowledge'>('knowledge')
  const [activeKnowledgeCategory, setActiveKnowledgeCategory] = useState<string>('all')
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([])
  const [chatInput, setChatInput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [editingItem, setEditingItem] = useState<string | null>(null)
  const [showGenerator, setShowGenerator] = useState(false)
  const [newItem, setNewItem] = useState<{
    type: KnowledgeItem['type']
    title: string
    content: string
    tags: string[]
    importance: 'low' | 'medium' | 'high'
  }>({
    type: 'note',
    title: '',
    content: '',
    tags: [],
    importance: 'medium'
  })

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const chatInputRef = useRef<HTMLInputElement>(null)

  // Knowledge categories with comprehensive story elements
  const knowledgeCategories = [
    { id: 'all', label: 'All Items', icon: Layers, color: 'text-warm-600' },
    { id: 'character', label: 'Characters', icon: Users, color: 'text-blue-600' },
    { id: 'location', label: 'Locations', icon: MapPin, color: 'text-green-600' },
    { id: 'story-arc', label: 'Story Arcs', icon: GitBranch, color: 'text-purple-600' },
    { id: 'theme', label: 'Themes', icon: Heart, color: 'text-red-600' },
    { id: 'conflict', label: 'Conflicts', icon: Sword, color: 'text-orange-600' },
    { id: 'world-building', label: 'World Building', icon: Globe, color: 'text-cyan-600' },
    { id: 'timeline', label: 'Timeline', icon: Clock, color: 'text-indigo-600' },
    { id: 'setting', label: 'Settings', icon: Palette, color: 'text-pink-600' },
    { id: 'plot-device', label: 'Plot Devices', icon: Target, color: 'text-yellow-600' },
    { id: 'research', label: 'Research', icon: FileText, color: 'text-gray-600' },
    { id: 'note', label: 'General Notes', icon: Lightbulb, color: 'text-amber-600' }
  ]

  useEffect(() => {
    if (activeTab === 'chat' && chatInputRef.current) {
      chatInputRef.current.focus()
    }
  }, [activeTab])

  useEffect(() => {
    // Auto-scroll chat to bottom
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [chatMessages])

  const handleSendMessage = async () => {
    if (!chatInput.trim() || isProcessing) return

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      role: 'user',
      content: chatInput,
      timestamp: new Date(),
      context: selectedText ? { selectedText } : undefined
    }

    setChatMessages(prev => [...prev, userMessage])
    setChatInput('')
    setIsProcessing(true)

    try {
      // Simulate AI response
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const aiResponse: ChatMessage = {
        id: `msg_${Date.now() + 1}`,
        role: 'assistant',
        content: `## Writing Analysis Complete

Based on your input: "${chatInput}"

### Suggestions:
- **Character Development**: Consider exploring the protagonist's internal conflict
- **Plot Structure**: This could serve as a turning point in Act II
- **World Building**: Add sensory details to enhance immersion
- **Dialogue**: Ensure each character has a distinct voice

### Next Steps:
1. Review existing knowledge base entries
2. Add new characters/locations if needed
3. Update story arc progression
4. Consider thematic implications

*This is a simulated response. In production, this would connect to your AI writing assistant.*`,
        timestamp: new Date()
      }

      setChatMessages(prev => [...prev, aiResponse])
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsProcessing(false)
    }
  }



  const addKnowledgeItem = () => {
    if (!newItem.title.trim() || !newItem.content.trim()) return

    const item: KnowledgeItem = {
      id: `item_${Date.now()}`,
      ...newItem,
      createdAt: new Date(),
      updatedAt: new Date(),
      connections: []
    }

    setKnowledgeItems(prev => [...prev, item])
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const filteredKnowledgeItems = knowledgeItems.filter(item =>
    activeKnowledgeCategory === 'all' || item.type === activeKnowledgeCategory
  )

  const handleGeneratedItems = (items: KnowledgeItem[]) => {
    setKnowledgeItems(prev => [...prev, ...items])
  }

  const handleEditItem = (itemId: string) => {
    const item = knowledgeItems.find(i => i.id === itemId)
    if (item) {
      setNewItem({
        type: item.type,
        title: item.title,
        content: item.content,
        tags: item.tags,
        importance: item.importance || 'medium'
      })
      setEditingItem(itemId)
    }
  }

  const handleUpdateItem = () => {
    if (!editingItem || !newItem.title.trim() || !newItem.content.trim()) return

    setKnowledgeItems(prev => prev.map(item =>
      item.id === editingItem
        ? { ...item, ...newItem, updatedAt: new Date() }
        : item
    ))

    setEditingItem(null)
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const handleCancelEdit = () => {
    setEditingItem(null)
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const getTypeIcon = (type: KnowledgeItem['type']) => {
    const category = knowledgeCategories.find(cat => cat.id === type)
    if (category) {
      const IconComponent = category.icon
      return <IconComponent className="h-4 w-4" />
    }
    return <Lightbulb className="h-4 w-4" />
  }

  const getTypeColor = (type: KnowledgeItem['type']) => {
    const category = knowledgeCategories.find(cat => cat.id === type)
    if (category) {
      return `bg-${category.color.split('-')[1]}-100 ${category.color} border-${category.color.split('-')[1]}-200`
    }
    return 'bg-warm-100 text-warm-700 border-warm-200'
  }

  const getImportanceColor = (importance: 'low' | 'medium' | 'high') => {
    switch (importance) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'low': return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  return (
    <div
      className="h-full flex flex-col border-warm-200 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-lg relative"
      style={{ width }}
    >
      <ResizeHandle />

      <div className="border-b border-warm-200/50 dark:border-gray-700/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Feather className="h-5 w-5 text-literary-amber" />
            <h2 className="text-lg font-literary font-semibold text-warm-800 dark:text-gray-200">
              Knowledge Base
            </h2>
            {knowledgeItems.length === 0 && (
              <Button
                size="sm"
                onClick={() => setShowGenerator(true)}
                className="ml-auto bg-blue-600 hover:bg-blue-700 text-white font-mono"
              >
                <Wand2 className="h-3 w-3 mr-1" />
                Initialize
              </Button>
            )}
          </div>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose} className="text-warm-600 hover:text-warm-800 dark:text-gray-400 dark:hover:text-gray-200">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'chat' | 'knowledge')} className="flex-1 flex flex-col">
          <div className="border-b border-warm-200/50 dark:border-gray-700/50">
            <div className="grid w-full grid-cols-2 bg-warm-50/50 dark:bg-gray-800/50">
              <button
                onClick={() => setActiveTab('chat')}
                className={`px-4 py-3 text-sm font-medium transition-colors ${
                  activeTab === 'chat'
                    ? 'bg-white dark:bg-gray-900 text-warm-800 dark:text-gray-200 border-b-2 border-literary-amber'
                    : 'text-warm-600 dark:text-gray-400 hover:text-warm-800 dark:hover:text-gray-200'
                }`}
              >
                <Bot className="h-4 w-4 mr-2 inline" />
                AI Assistant
              </button>
              <button
                onClick={() => setActiveTab('knowledge')}
                className={`px-4 py-3 text-sm font-medium transition-colors ${
                  activeTab === 'knowledge'
                    ? 'bg-white dark:bg-gray-900 text-warm-800 dark:text-gray-200 border-b-2 border-literary-amber'
                    : 'text-warm-600 dark:text-gray-400 hover:text-warm-800 dark:hover:text-gray-200'
                }`}
              >
                <BookOpen className="h-4 w-4 mr-2 inline" />
                Knowledge Base
              </button>
            </div>
          </div>

          <div className={`flex-1 flex flex-col ${activeTab === 'chat' ? '' : 'hidden'}`}>
            {/* Chat Messages */}
            <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
              {chatMessages.length === 0 ? (
                <div className="space-y-4">
                  <div className="text-center text-warm-600 dark:text-gray-400 mb-6">
                    <Bot className="h-12 w-12 mx-auto mb-3 opacity-50 text-literary-amber" />
                    <p className="font-mono text-lg mb-2">AI Writing Assistant</p>
                    <p className="text-sm font-mono">Get help with your story, characters, and plot development</p>
                  </div>

                  <div className="space-y-2">
                    <p className="text-xs font-medium text-warm-600 dark:text-gray-400 font-mono">Try asking:</p>
                    {[
                      "Help me develop this character's backstory",
                      "What should happen next in this scene?",
                      "Improve this dialogue to sound more natural",
                      "Add more description to this setting",
                      "How can I increase tension in this chapter?"
                    ].map((prompt, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start text-xs h-auto p-3 text-left bg-warm-50 dark:bg-gray-800 hover:bg-warm-100 dark:hover:bg-gray-700 border border-warm-200/50 dark:border-gray-600 font-mono"
                        onClick={() => setChatInput(prompt)}
                      >
                        {prompt}
                      </Button>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {chatMessages.map((message) => (
                    <div key={message.id} className="space-y-2">
                      <div className="flex items-center gap-2">
                        {message.role === 'user' ? (
                          <User className="h-4 w-4 text-warm-700 dark:text-gray-300" />
                        ) : (
                          <Bot className="h-4 w-4 text-literary-amber" />
                        )}
                        <span className="text-xs font-medium text-warm-800 dark:text-gray-200 font-mono">
                          {message.role === 'user' ? 'writer' : 'ai-assistant'}
                        </span>
                        <span className="text-xs text-warm-500 dark:text-gray-500 font-mono">
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                      </div>

                      {message.context?.selectedText && (
                        <div className="ml-6 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg text-xs border border-gray-200 dark:border-gray-700">
                          <span className="font-medium text-gray-700 dark:text-gray-300 font-mono">Selected text:</span>
                          <div className="font-mono text-gray-800 dark:text-gray-200 mt-1 whitespace-pre-wrap">
                            "&quot;{message.context.selectedText.slice(0, 200)}...&quot;"
                          </div>
                        </div>
                      )}

                      <div className="ml-6 prose prose-sm max-w-none dark:prose-invert">
                        <ReactMarkdown
                          components={{
                            p: ({ children }) => <p className="text-warm-700 dark:text-gray-300 font-mono text-sm">{children}</p>,
                            h2: ({ children }) => <h2 className="text-warm-800 dark:text-gray-200 font-mono text-base font-semibold">{children}</h2>,
                            h3: ({ children }) => <h3 className="text-warm-800 dark:text-gray-200 font-mono text-sm font-semibold">{children}</h3>,
                            ul: ({ children }) => <ul className="text-warm-700 dark:text-gray-300 font-mono text-sm">{children}</ul>,
                            li: ({ children }) => <li className="text-warm-700 dark:text-gray-300 font-mono text-sm">{children}</li>
                          }}
                        >
                          {message.content}
                        </ReactMarkdown>
                      </div>
                    </div>
                  ))}

                  {isProcessing && (
                    <div className="flex items-center gap-2 ml-6 text-literary-amber">
                      <Bot className="h-4 w-4 animate-pulse" />
                      <span className="text-sm font-mono">Processing request...</span>
                    </div>
                  )}
                </div>
              )}
            </ScrollArea>

            {/* Chat Input */}
            <div className="border-t border-warm-200/50 dark:border-gray-700/50 p-4">
              {selectedText && (
                <div className="mb-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg text-xs border border-gray-200 dark:border-gray-700">
                  <span className="font-medium text-gray-700 dark:text-gray-300 font-mono">Selected text:</span>
                  <div className="font-mono text-gray-800 dark:text-gray-200 mt-1 whitespace-pre-wrap">
                    &quot;{selectedText.slice(0, 100)}{selectedText.length > 100 ? '...' : ''}&quot;
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                <Input
                  ref={chatInputRef}
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      handleSendMessage()
                    }
                  }}
                  placeholder="Ask about your writing, request edits, or get suggestions..."
                  disabled={isProcessing}
                  className="font-mono text-sm bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!chatInput.trim() || isProcessing}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <div className={`flex-1 flex flex-col ${activeTab === 'knowledge' ? '' : 'hidden'}`}>
            {/* Knowledge Categories */}
            <div className="border-b border-warm-200/50 dark:border-gray-700/50 p-4">
              <div className="grid grid-cols-2 gap-2 mb-4">
                {knowledgeCategories.map((category) => {
                  const IconComponent = category.icon
                  const isActive = activeKnowledgeCategory === category.id
                  return (
                    <button
                      key={category.id}
                      onClick={() => setActiveKnowledgeCategory(category.id)}
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg text-xs font-mono transition-colors ${
                        isActive
                          ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700'
                          : 'bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                      }`}
                    >
                      <IconComponent className="h-3 w-3" />
                      {category.label}
                      <span className="ml-auto text-xs opacity-60">
                        {filteredKnowledgeItems.length > 0 && activeKnowledgeCategory === category.id
                          ? filteredKnowledgeItems.length
                          : knowledgeItems.filter(item => item.type === category.id).length || 0}
                      </span>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Knowledge Items */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {/* Add New Item Form */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 p-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <select
                        value={newItem.type}
                        onChange={(e) => setNewItem(prev => ({ ...prev, type: e.target.value as KnowledgeItem['type'] }))}
                        className="px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-900 text-sm font-mono"
                      >
                        {knowledgeCategories.slice(1).map(category => (
                          <option key={category.id} value={category.id}>{category.label}</option>
                        ))}
                      </select>
                      <select
                        value={newItem.importance}
                        onChange={(e) => setNewItem(prev => ({ ...prev, importance: e.target.value as 'low' | 'medium' | 'high' }))}
                        className="px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-900 text-sm font-mono"
                      >
                        <option value="low">Low Priority</option>
                        <option value="medium">Medium Priority</option>
                        <option value="high">High Priority</option>
                      </select>
                    </div>
                    <Input
                      placeholder="Enter character name, location, or item title..."
                      value={newItem.title}
                      onChange={(e) => setNewItem(prev => ({ ...prev, title: e.target.value }))}
                      className="text-sm font-mono bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-600"
                    />
                    <Textarea
                      placeholder="Add detailed description, backstory, or notes about this story element..."
                      value={newItem.content}
                      onChange={(e) => setNewItem(prev => ({ ...prev, content: e.target.value }))}
                      className="min-h-[80px] text-sm font-mono bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-600"
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={editingItem ? handleUpdateItem : addKnowledgeItem}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700 text-white font-mono"
                        disabled={!newItem.title.trim() || !newItem.content.trim()}
                      >
                        {editingItem ? (
                          <>
                            <Save className="h-4 w-4 mr-1" />
                            Update Item
                          </>
                        ) : (
                          <>
                            <Plus className="h-4 w-4 mr-1" />
                            Add to Story
                          </>
                        )}
                      </Button>
                      {editingItem && (
                        <Button
                          onClick={handleCancelEdit}
                          size="sm"
                          variant="outline"
                          className="font-mono"
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Knowledge Items List */}
                {filteredKnowledgeItems.length === 0 ? (
                  <div className="text-center text-gray-600 dark:text-gray-400 py-8">
                    <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="font-mono text-lg mb-2">No items in {activeKnowledgeCategory === 'all' ? 'knowledge base' : activeKnowledgeCategory}</p>
                    <p className="text-sm font-mono">Start building your story&apos;s knowledge base</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredKnowledgeItems.map((item) => (
                      <div key={item.id} className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 hover:shadow-md transition-shadow">
                        <div className="p-4">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {getTypeIcon(item.type)}
                              <h4 className="font-mono font-semibold text-gray-800 dark:text-gray-200">{item.title}</h4>
                              <Badge className={`${getTypeColor(item.type)} font-mono text-xs`}>
                                {item.type}
                              </Badge>
                              {item.importance && (
                                <Badge className={`${getImportanceColor(item.importance)} font-mono text-xs`}>
                                  {item.importance}
                                </Badge>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditItem(item.id)}
                              className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
                            >
                              <Edit3 className="h-3 w-3" />
                            </Button>
                          </div>
                          <pre className="text-sm text-gray-700 dark:text-gray-300 font-mono leading-relaxed whitespace-pre-wrap bg-gray-50 dark:bg-gray-800 p-3 rounded border">
                            {item.content}
                          </pre>
                          <div className="flex items-center justify-between mt-3 text-xs text-gray-500 dark:text-gray-400 font-mono">
                            <span>last modified: {item.updatedAt.toLocaleDateString()}</span>
                            <span>id: {item.id.slice(-8)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </Tabs>
      </div>

      {/* Knowledge Generator Modal */}
      {showGenerator && (
        <KnowledgeGenerator
          projectId={projectId}
          onGenerated={handleGeneratedItems}
          onClose={() => setShowGenerator(false)}
        />
      )}
    </div>
  )
}
