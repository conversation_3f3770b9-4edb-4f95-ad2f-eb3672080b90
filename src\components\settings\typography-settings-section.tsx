/**
 * Typography Settings Section
 * Font and text size customization
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Type, 
  Monitor, 
  BookOpen, 
  Edit3,
  Eye,
  RotateCcw
} from 'lucide-react';

import { useTypographySettings } from '@/lib/settings/settings-store';
import { 
  textSizeMap, 
  fontFamilyMap,
  lineHeightMap,
  letterSpacingMap,
  defaultTypographySettings 
} from '@/lib/settings/settings-types';

const textSizeOptions = [
  { value: 'small', label: 'Small', description: 'Compact text for more content' },
  { value: 'medium', label: 'Medium', description: 'Standard comfortable reading' },
  { value: 'large', label: 'Large', description: 'Larger text for better readability' },
  { value: 'extra-large', label: 'Extra Large', description: 'Maximum readability' },
  { value: 'custom', label: 'Custom', description: 'Set your own size' },
] as const;

const editorFontOptions = [
  { value: 'jetbrains-mono', label: 'JetBrains Mono', description: 'Modern coding font with ligatures' },
  { value: 'fira-code', label: 'Fira Code', description: 'Popular programming font' },
  { value: 'source-code-pro', label: 'Source Code Pro', description: 'Adobe\'s monospace font' },
  { value: 'consolas', label: 'Consolas', description: 'Microsoft\'s coding font' },
  { value: 'monaco', label: 'Monaco', description: 'Classic monospace font' },
] as const;

const uiFontOptions = [
  { value: 'inter', label: 'Inter', description: 'Modern UI font with excellent readability' },
  { value: 'roboto', label: 'Roboto', description: 'Google\'s material design font' },
  { value: 'open-sans', label: 'Open Sans', description: 'Friendly and readable' },
  { value: 'system-ui', label: 'System UI', description: 'Use system default font' },
  { value: 'segoe-ui', label: 'Segoe UI', description: 'Microsoft\'s system font' },
] as const;

const readingFontOptions = [
  { value: 'crimson-text', label: 'Crimson Text', description: 'Elegant serif for manuscripts' },
  { value: 'georgia', label: 'Georgia', description: 'Classic web-safe serif' },
  { value: 'times-new-roman', label: 'Times New Roman', description: 'Traditional serif font' },
  { value: 'libre-baskerville', label: 'Libre Baskerville', description: 'Modern Baskerville revival' },
  { value: 'merriweather', label: 'Merriweather', description: 'Designed for screens' },
] as const;

const lineHeightOptions = [
  { value: 'compact', label: 'Compact', description: '1.3x - Tight spacing' },
  { value: 'normal', label: 'Normal', description: '1.5x - Standard spacing' },
  { value: 'relaxed', label: 'Relaxed', description: '1.6x - Comfortable spacing' },
  { value: 'loose', label: 'Loose', description: '1.8x - Maximum spacing' },
] as const;

const letterSpacingOptions = [
  { value: 'tight', label: 'Tight', description: 'Condensed letter spacing' },
  { value: 'normal', label: 'Normal', description: 'Standard letter spacing' },
  { value: 'wide', label: 'Wide', description: 'Expanded letter spacing' },
] as const;

export function TypographySettingsSection() {
  const { typography, updateTypography } = useTypographySettings();
  const [customSize, setCustomSize] = useState(typography.customTextSize || 14);

  const handleTextSizeChange = (size: typeof typography.textSize) => {
    updateTypography({ textSize: size });
  };

  const handleCustomSizeChange = (value: number[]) => {
    const newSize = value[0];
    setCustomSize(newSize);
    updateTypography({ 
      textSize: 'custom',
      customTextSize: newSize 
    });
  };

  const resetTypography = () => {
    updateTypography(defaultTypographySettings);
    setCustomSize(14);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Type className="w-5 h-5 text-primary" />
            <h3 className="text-lg font-semibold">Typography</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={resetTypography}
            className="text-xs"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Reset
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Customize fonts and text sizing for optimal reading and writing experience.
        </p>
      </div>

      {/* Text Size */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Eye className="w-4 h-4" />
            Text Size
          </CardTitle>
          <CardDescription>
            Adjust the size of text throughout the application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup
            value={typography.textSize}
            onValueChange={handleTextSizeChange}
            className="grid grid-cols-2 gap-4"
          >
            {textSizeOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={option.value} />
                <Label htmlFor={option.value} className="cursor-pointer flex-1">
                  <div className="font-medium">{option.label}</div>
                  <div className="text-xs text-muted-foreground">
                    {option.description}
                  </div>
                </Label>
              </div>
            ))}
          </RadioGroup>

          {typography.textSize === 'custom' && (
            <div className="space-y-3 p-4 border rounded-lg bg-muted/30">
              <Label className="text-sm font-medium">Custom Size: {customSize}px</Label>
              <Slider
                value={[customSize]}
                onValueChange={handleCustomSizeChange}
                min={10}
                max={24}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>10px</span>
                <span>24px</span>
              </div>
            </div>
          )}

          {/* Preview */}
          <div className="p-4 border rounded-lg bg-card">
            <div className="text-xs text-muted-foreground mb-2">Preview:</div>
            <div 
              className="space-y-2"
              style={{
                fontSize: typography.textSize === 'custom' 
                  ? `${customSize}px` 
                  : textSizeMap[typography.textSize]?.ui || '14px'
              }}
            >
              <div>This is how your UI text will appear.</div>
              <div className="text-muted-foreground">
                Secondary text and descriptions will look like this.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Font Families */}
      <div className="space-y-4">
        <h4 className="font-semibold flex items-center gap-2">
          <Type className="w-4 h-4" />
          Font Families
        </h4>

        {/* Editor Font */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Edit3 className="w-4 h-4" />
              Editor Font
            </CardTitle>
            <CardDescription>
              Monospace font used in the writing editor
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={typography.editorFont}
              onValueChange={(value) => updateTypography({ editorFont: value as any })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {editorFontOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Font Preview */}
            <div className="mt-3 p-3 border rounded bg-card">
              <div className="text-xs text-muted-foreground mb-1">Preview:</div>
              <div 
                className="font-mono text-sm"
                style={{ fontFamily: fontFamilyMap[typography.editorFont] }}
              >
                function writeStory() {'{'}
                <br />
                &nbsp;&nbsp;return "Once upon a time...";
                <br />
                {'}'}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* UI Font */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Monitor className="w-4 h-4" />
              Interface Font
            </CardTitle>
            <CardDescription>
              Sans-serif font used for menus, buttons, and UI elements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={typography.uiFont}
              onValueChange={(value) => updateTypography({ uiFont: value as any })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {uiFontOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Font Preview */}
            <div className="mt-3 p-3 border rounded bg-card">
              <div className="text-xs text-muted-foreground mb-1">Preview:</div>
              <div 
                className="text-sm space-y-1"
                style={{ fontFamily: fontFamilyMap[typography.uiFont] }}
              >
                <div className="font-semibold">Navigation Menu</div>
                <div>Settings and preferences</div>
                <Badge variant="outline" className="text-xs">New Feature</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reading Font */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              Reading Font
            </CardTitle>
            <CardDescription>
              Serif font used for manuscript preview and reading modes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={typography.readingFont}
              onValueChange={(value) => updateTypography({ readingFont: value as any })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {readingFontOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Font Preview */}
            <div className="mt-3 p-3 border rounded bg-card">
              <div className="text-xs text-muted-foreground mb-1">Preview:</div>
              <div 
                className="text-sm leading-relaxed"
                style={{ fontFamily: fontFamilyMap[typography.readingFont] }}
              >
                <p className="font-semibold mb-2">Chapter One</p>
                <p>
                  The old lighthouse stood sentinel against the storm, its beacon cutting through 
                  the darkness like a sword of light. Sarah pulled her coat tighter as she 
                  approached the weathered door.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
