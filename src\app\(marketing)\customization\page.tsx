/**
 * Customization Page
 * Comprehensive visual customization hub for BookScribe AI
 */

import { CustomizationHub } from '@/components/customization/customization-hub'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { SettingsButton } from '@/components/settings/settings-modal'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, Palette, Settings } from 'lucide-react'
import Link from 'next/link'

export default function CustomizationPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 dark:from-stone-950 dark:via-amber-950/20 dark:to-stone-950">
      {/* Header */}
      <header className="border-b border-amber-200/30 dark:border-amber-900/30 bg-white/80 dark:bg-stone-950/90 backdrop-blur-xl">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <div className="flex items-center gap-2">
                <Palette className="h-6 w-6 text-primary" />
                <h1 className="text-xl font-bold" style={{ fontFamily: 'Crimson Text, serif' }}>
                  BookScribe AI Customization
                </h1>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <ThemeToggle />
              <SettingsButton />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Introduction */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4" style={{ fontFamily: 'Crimson Text, serif' }}>
              Customize Your Writing Experience
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Personalize BookScribe AI's visual appearance with our collection of carefully crafted themes.
              Choose your preferred theme mode and discover the perfect writing environment for your creative process.
            </p>
          </div>

          {/* Customization Hub */}
          <CustomizationHub />

          {/* Features */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <Palette className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Visual Themes</h3>
              <p className="text-sm text-muted-foreground">
                Choose from carefully crafted light and dark themes, each designed for different writing moods and environments.
              </p>
            </div>

            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <Settings className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Smart Preferences</h3>
              <p className="text-sm text-muted-foreground">
                Automatically adapts to your system preferences or manually select your preferred theme mode and style.
              </p>
            </div>

            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="font-semibold mb-2">Writer-Focused</h3>
              <p className="text-sm text-muted-foreground">
                Every theme is crafted with writers in mind, optimizing readability and reducing eye strain for long writing sessions.
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center">
            <div className="bg-card border rounded-lg p-8 max-w-2xl mx-auto">
              <h3 className="text-xl font-semibold mb-4" style={{ fontFamily: 'Crimson Text, serif' }}>
                Ready to Start Writing?
              </h3>
              <p className="text-muted-foreground mb-6">
                Customize your perfect writing environment and begin your creative journey with BookScribe AI.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/demo">
                  <Button size="lg" className="w-full sm:w-auto">
                    Try Demo
                  </Button>
                </Link>
                <Link href="/signup">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Get Started Free
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-amber-200/30 dark:border-amber-900/30 bg-white/50 dark:bg-stone-950/50 backdrop-blur-sm mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-sm text-muted-foreground">
            <p>© 2024 BookScribe AI. Crafted for writers, by writers.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
