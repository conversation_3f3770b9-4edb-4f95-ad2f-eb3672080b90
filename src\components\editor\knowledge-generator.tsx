'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Wand2, 
  FileText, 
  Users, 
  MapPin, 
  BookOpen, 
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface KnowledgeGeneratorProps {
  projectId: string
  onGenerated: (items: any[]) => void
  onClose: () => void
}

interface GenerationPrompt {
  id: string
  category: string
  prompt: string
  example: string
}

export function KnowledgeGenerator({ projectId, onGenerated, onClose }: KnowledgeGeneratorProps) {
  const [generationType, setGenerationType] = useState<'blank' | 'ai-generated'>('blank')
  const [projectDescription, setProjectDescription] = useState('')
  const [genre, setGenre] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [generatedItems, setGeneratedItems] = useState<any[]>([])

  const generationPrompts: GenerationPrompt[] = [
    {
      id: 'project-overview',
      category: 'Project Setup',
      prompt: 'Describe your project in a few sentences',
      example: 'A React-based task management application with real-time collaboration features'
    },
    {
      id: 'main-characters',
      category: 'Core Components',
      prompt: 'What are the main components/modules?',
      example: 'TaskList, UserAuth, RealtimeSync, NotificationSystem'
    },
    {
      id: 'setting',
      category: 'Architecture',
      prompt: 'Describe the technical architecture',
      example: 'Next.js frontend, Node.js API, PostgreSQL database, WebSocket connections'
    },
    {
      id: 'themes',
      category: 'Key Features',
      prompt: 'What are the core features and functionality?',
      example: 'Task creation/editing, team collaboration, deadline tracking, file attachments'
    }
  ]

  const handleGenerateKnowledge = async () => {
    if (!projectDescription.trim()) return

    setIsGenerating(true)
    setGenerationProgress(0)

    try {
      // Simulate AI generation process
      const items = []
      
      // Generate project documentation
      setGenerationProgress(25)
      items.push({
        id: `gen_${Date.now()}_1`,
        type: 'research',
        title: 'Project Overview',
        content: `# ${projectDescription}

## Technical Stack
- Frontend: React/Next.js
- Backend: Node.js/Express
- Database: PostgreSQL
- Deployment: Vercel/AWS

## Development Approach
- Agile methodology
- Test-driven development
- Continuous integration/deployment
- Code review process`,
        importance: 'high',
        createdAt: new Date(),
        updatedAt: new Date(),
        connections: []
      })

      // Generate component structure
      setGenerationProgress(50)
      items.push({
        id: `gen_${Date.now()}_2`,
        type: 'character',
        title: 'Core Components',
        content: `// Main application components

interface AppComponent {
  name: string;
  responsibility: string;
  dependencies: string[];
}

const coreComponents: AppComponent[] = [
  {
    name: "UserInterface",
    responsibility: "Handle user interactions and display",
    dependencies: ["StateManager", "APIClient"]
  },
  {
    name: "StateManager", 
    responsibility: "Manage application state and data flow",
    dependencies: ["DataStore"]
  },
  {
    name: "APIClient",
    responsibility: "Handle server communication",
    dependencies: ["AuthService"]
  }
];`,
        importance: 'high',
        createdAt: new Date(),
        updatedAt: new Date(),
        connections: []
      })

      // Generate API documentation
      setGenerationProgress(75)
      items.push({
        id: `gen_${Date.now()}_3`,
        type: 'world-building',
        title: 'API Architecture',
        content: `# API Endpoints Documentation

## Authentication
- POST /api/auth/login
- POST /api/auth/register
- POST /api/auth/logout
- GET /api/auth/profile

## Core Resources
- GET /api/resources
- POST /api/resources
- PUT /api/resources/:id
- DELETE /api/resources/:id

## Data Models
\`\`\`typescript
interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  createdAt: Date;
}

interface Resource {
  id: string;
  title: string;
  content: string;
  userId: string;
  status: 'draft' | 'published';
}
\`\`\``,
        importance: 'medium',
        createdAt: new Date(),
        updatedAt: new Date(),
        connections: []
      })

      // Generate testing strategy
      setGenerationProgress(90)
      items.push({
        id: `gen_${Date.now()}_4`,
        type: 'plot-device',
        title: 'Testing Strategy',
        content: `# Testing Implementation Plan

## Unit Tests
- Component testing with Jest/React Testing Library
- Function/utility testing
- API endpoint testing

## Integration Tests
- Database integration tests
- API integration tests
- End-to-end user flows

## Test Coverage Goals
- Minimum 80% code coverage
- Critical path 100% coverage
- Automated testing in CI/CD

## Testing Tools
- Jest for unit tests
- Cypress for E2E tests
- MSW for API mocking
- Testing Library for component tests`,
        importance: 'medium',
        createdAt: new Date(),
        updatedAt: new Date(),
        connections: []
      })

      setGenerationProgress(100)
      setGeneratedItems(items)
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
    } catch (error) {
      console.error('Generation failed:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleAcceptGeneration = () => {
    onGenerated(generatedItems)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-mono">
            <Wand2 className="h-5 w-5 text-blue-600" />
            Initialize Knowledge Base
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Generation Type Selection */}
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => setGenerationType('blank')}
              className={`p-4 rounded-lg border-2 transition-colors ${
                generationType === 'blank'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
              }`}
            >
              <FileText className="h-8 w-8 mx-auto mb-2 text-gray-600" />
              <h3 className="font-mono font-semibold">Blank Project</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Start with empty knowledge base
              </p>
            </button>

            <button
              onClick={() => setGenerationType('ai-generated')}
              className={`p-4 rounded-lg border-2 transition-colors ${
                generationType === 'ai-generated'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
              }`}
            >
              <Zap className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <h3 className="font-mono font-semibold">AI Generated</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Generate from project description
              </p>
            </button>
          </div>

          {/* AI Generation Form */}
          {generationType === 'ai-generated' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-mono font-medium mb-2">
                  Project Description
                </label>
                <Textarea
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  placeholder="// Describe your project, its purpose, and key features..."
                  className="font-mono"
                  rows={4}
                />
              </div>

              <div>
                <label className="block text-sm font-mono font-medium mb-2">
                  Project Type/Genre
                </label>
                <Input
                  value={genre}
                  onChange={(e) => setGenre(e.target.value)}
                  placeholder="e.g., Web Application, Mobile App, API Service..."
                  className="font-mono"
                />
              </div>

              {/* Generation Progress */}
              {isGenerating && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                    <span className="text-sm font-mono">Generating knowledge base...</span>
                  </div>
                  <Progress value={generationProgress} className="w-full" />
                </div>
              )}

              {/* Generated Items Preview */}
              {generatedItems.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-mono font-semibold">Generated Items:</h4>
                  <div className="grid grid-cols-1 gap-2 max-h-60 overflow-y-auto">
                    {generatedItems.map((item) => (
                      <div key={item.id} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-mono text-sm">{item.title}</span>
                        <Badge className="ml-auto font-mono text-xs">
                          {item.type}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose} className="font-mono">
              Cancel
            </Button>
            
            {generationType === 'blank' ? (
              <Button onClick={() => { onGenerated([]); onClose(); }} className="font-mono">
                Create Blank Project
              </Button>
            ) : (
              <>
                {generatedItems.length === 0 ? (
                  <Button 
                    onClick={handleGenerateKnowledge}
                    disabled={!projectDescription.trim() || isGenerating}
                    className="font-mono"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-4 w-4 mr-2" />
                        Generate Knowledge Base
                      </>
                    )}
                  </Button>
                ) : (
                  <Button onClick={handleAcceptGeneration} className="font-mono">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Accept & Continue
                  </Button>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
