"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  PenTool, 
  BookOpen, 
  Users, 
  Brain, 
  Zap, 
  BarChart3, 
  Play, 
  ChevronRight,
  Star,
  Clock,
  Target,
  Sparkles,
  Globe,
  Layers,
  ArrowRight
} from "lucide-react";
import { DemoWizard } from "@/components/demo/demo-wizard";
import { DemoEditor } from "@/components/demo/demo-editor";
import { DemoAgents } from "@/components/demo/demo-agents";
import { DemoStoryBible } from "@/components/demo/demo-story-bible";
import { DemoAnalytics } from "@/components/demo/demo-analytics";

export default function DemoPage() {
  const [activeDemo, setActiveDemo] = useState<string>("overview");

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Animated gradient background */}
      <div className="fixed inset-0 bg-gradient-to-br from-neutral-950 via-neutral-900 to-amber-950/20" />
      
      {/* Header */}
      <header className="relative z-50 border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-20 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-orange-600 blur-lg opacity-50" />
              <div className="relative w-10 h-10 bg-gradient-to-br from-amber-600 to-orange-600 rounded-lg flex items-center justify-center">
                <PenTool className="w-6 h-6 text-white" />
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight">
              BookScribe AI
            </h1>
          </div>
          
          <nav className="flex gap-2 sm:gap-4">
            <a href="/">
              <Button variant="ghost" className="text-white/80 hover:text-white hover:bg-white/10 font-medium">
                ← Back to Home
              </Button>
            </a>
            <a href="/signup">
              <Button className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg font-medium px-6">
                Start Writing Free
                <ChevronRight className="w-4 h-4 ml-1" />
              </Button>
            </a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="container max-w-6xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-amber-500/20 bg-amber-500/10 mb-8">
            <Play className="w-4 h-4 text-amber-500" />
            <span className="text-sm text-amber-200">Interactive Demo</span>
          </div>

          <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Experience BookScribe AI
            </span>
            <br />
            <span className="bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 bg-clip-text text-transparent">
              In Action
            </span>
          </h2>
          
          <p className="text-lg sm:text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed mb-12">
            Explore the most advanced AI writing platform for novelists. See how our AI agents, 
            intelligent context engine, and collaborative tools work together to help you write your masterpiece.
          </p>

          {/* Demo Navigation */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-12">
            {[
              { id: "wizard", title: "Project Setup", icon: Sparkles, desc: "AI-guided project creation" },
              { id: "editor", title: "Writing Interface", icon: PenTool, desc: "Smart editor with AI assistance" },
              { id: "agents", title: "AI Agents", icon: Brain, desc: "Multi-agent writing pipeline" },
              { id: "story-bible", title: "Story Bible", icon: BookOpen, desc: "Character & world management" },
              { id: "analytics", title: "Analytics", icon: BarChart3, desc: "Progress tracking & insights" }
            ].map((demo) => (
              <Card 
                key={demo.id}
                className={`cursor-pointer transition-all duration-300 hover:scale-105 border ${
                  activeDemo === demo.id 
                    ? 'border-amber-500/50 bg-amber-500/10' 
                    : 'border-white/10 bg-white/5 hover:border-white/20'
                }`}
                onClick={() => setActiveDemo(demo.id)}
              >
                <CardContent className="p-6 text-center">
                  <demo.icon className={`w-8 h-8 mx-auto mb-3 ${
                    activeDemo === demo.id ? 'text-amber-500' : 'text-white/70'
                  }`} />
                  <h3 className="font-semibold mb-2">{demo.title}</h3>
                  <p className="text-sm text-gray-400">{demo.desc}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Content */}
      <section className="relative z-10 py-12 px-4">
        <div className="container max-w-7xl mx-auto">
          <div className="min-h-[600px]">
            {activeDemo === "overview" && <DemoOverview />}
            {activeDemo === "wizard" && <DemoWizard />}
            {activeDemo === "editor" && <DemoEditor />}
            {activeDemo === "agents" && <DemoAgents />}
            {activeDemo === "story-bible" && <DemoStoryBible />}
            {activeDemo === "analytics" && <DemoAnalytics />}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20 px-4 border-t border-white/10">
        <div className="container max-w-4xl mx-auto text-center">
          <h3 className="text-3xl sm:text-4xl font-bold mb-6">
            Ready to Write Your 
            <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent"> Masterpiece?</span>
          </h3>
          <p className="text-lg text-gray-400 mb-8 max-w-2xl mx-auto">
            Join thousands of authors who are already using BookScribe AI to create compelling narratives with unprecedented scale and consistency.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/signup">
              <Button 
                size="lg" 
                className="group relative overflow-hidden bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-2xl font-medium px-8 py-6 text-lg rounded-xl transition-all duration-300"
              >
                <span className="relative z-10 flex items-center">
                  <Sparkles className="w-5 h-5 mr-2" />
                  Start Writing Free
                </span>
                <div className="absolute inset-0 bg-white/20 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300" />
              </Button>
            </a>
            <a href="/login">
              <Button 
                size="lg" 
                variant="outline" 
                className="border-white/20 text-white hover:bg-white/10 font-medium px-8 py-6 text-lg rounded-xl backdrop-blur-sm"
              >
                Sign In
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 border-t border-white/10 py-12 bg-black/50 backdrop-blur-xl">
        <div className="container text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-amber-600 to-orange-600 rounded-lg flex items-center justify-center">
              <PenTool className="w-4 h-4 text-white" />
            </div>
            <span className="text-lg font-semibold">
              BookScribe AI
            </span>
          </div>
          <p className="text-gray-400 text-sm mb-4">
            © 2025 BookScribe AI. Empowering authors to write without limits.
          </p>
          <p className="text-xs text-amber-400">
            This demo showcases simulated features. Sign up to experience the real AI-powered writing platform.
          </p>
        </div>
      </footer>
    </div>
  );
}

// Demo Overview Component
function DemoOverview() {
  return (
    <div className="text-center">
      <h3 className="text-2xl font-bold mb-8">Choose a demo above to explore BookScribe AI's features</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-white/10 bg-white/5">
          <CardContent className="p-6">
            <Target className="w-12 h-12 text-amber-500 mx-auto mb-4" />
            <h4 className="font-semibold mb-2">300,000+ Words</h4>
            <p className="text-sm text-gray-400">Write epic novels with perfect consistency across massive word counts</p>
          </CardContent>
        </Card>
        <Card className="border-white/10 bg-white/5">
          <CardContent className="p-6">
            <Clock className="w-12 h-12 text-blue-500 mx-auto mb-4" />
            <h4 className="font-semibold mb-2">Real-time AI</h4>
            <p className="text-sm text-gray-400">Get instant suggestions and improvements as you write</p>
          </CardContent>
        </Card>
        <Card className="border-white/10 bg-white/5">
          <CardContent className="p-6">
            <Star className="w-12 h-12 text-purple-500 mx-auto mb-4" />
            <h4 className="font-semibold mb-2">Your Voice</h4>
            <p className="text-sm text-gray-400">AI learns and adapts to your unique writing style</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
