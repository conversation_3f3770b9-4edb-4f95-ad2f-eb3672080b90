{"permissions": {"allow": ["Bash(npx create-next-app:*)", "Bash(npx --yes create-next-app@latest . --typescript --tailwind --app --src-dir --import-alias=\"@/*\" --eslint)", "Bash(npx --yes create-next-app@latest bookscribe-app --typescript --tailwind --app --src-dir --import-alias=\"@/*\" --eslint)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "Bash(ls:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx shadcn@latest add:*)", "Bash(npm run build:*)", "Bash(npx next:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(npm run typecheck:*)", "<PERSON><PERSON>(true)", "Bash(npm run:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(touch:*)", "Bash(for step in structure-pacing-step character-world-step themes-content-step technical-specs-step review-step)", "Bash(do)", "Bash(done)", "Bash(rg:*)", "Bash(cp:*)", "Bash(/dev/null)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npm:*)", "Bash(node:*)", "Bash(npx ts-node -e \"\nimport { runSchemaTests } from './src/lib/validation/test-schemas';\nrunSchemaTests();\n\")", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git config:*)", "Bash(npx madge:*)", "Bash(NODE_OPTIONS='--max-old-space-size=8192' timeout 30s npx next build --debug 2 >& 1)", "<PERSON><PERSON>(mv:*)", "Bash(npx depcheck:*)", "Bash(awk:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(echo)", "Bash(for file in character-development-grid.tsx development-dimension-selector.tsx arc-prediction-panel.tsx character-arc-visualizer.tsx)", "Bash(do echo \"- src/components/analysis/$file\")", "Bash(git reset:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/mnt/c/Users/<USER>/BookScribe/fix-type-imports.sh:*)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(NODE_OPTIONS=--max-old-space-size=4096 npx next build)", "WebFetch(domain:supabase.com)", "Bash(NODE_OPTIONS='--max-old-space-size=4096' NEXT_TELEMETRY_DISABLED=1 npx next build --debug 2 >& 1)", "<PERSON><PERSON>(killall:*)", "Bash(for file in src/lib/services/ai-orchestrator.ts src/lib/services/content-generator.ts src/lib/services/semantic-search.ts src/lib/supabase/middleware.ts src/lib/auth/server.ts)", "Bash(do echo \"=== $file ===\")", "Bash(for file in src/app/api/subscription/portal/route.ts src/components/wizard/wizard-wrapper.tsx src/components/editor/editor-wrapper.tsx src/components/dashboard/dashboard-wrapper.tsx src/app/error.tsx)", "Bash(for:*)", "Bash(wc:*)", "<PERSON><PERSON>(python3:*)", "Bash(npx @axe-core/cli:*)", "Bash(PORT=3000 npm run dev 2 >& 1)", "Bash(NEXT_CACHE_DISABLED=1 npm run dev)"], "deny": []}}