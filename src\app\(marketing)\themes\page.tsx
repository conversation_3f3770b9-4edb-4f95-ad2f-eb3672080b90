/**
 * Theme Demo Page
 * Showcases all available themes in BookScribe AI
 */

import { ThemeShowcase } from '@/components/theme/theme-showcase'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Palette } from 'lucide-react'
import Link from 'next/link'

export default function ThemesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 dark:from-stone-950 dark:via-amber-950/20 dark:to-stone-950">
      {/* Header */}
      <header className="border-b border-amber-200/30 dark:border-amber-900/30 bg-white/80 dark:bg-stone-950/90 backdrop-blur-xl">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <div className="flex items-center gap-2">
                <Palette className="h-6 w-6 text-primary" />
                <h1 className="text-xl font-bold" style={{ fontFamily: 'Crimson Text, serif' }}>
                  BookScribe AI Themes
                </h1>
              </div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Introduction */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4" style={{ fontFamily: 'Crimson Text, serif' }}>
              Discover Your Perfect Writing Environment
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose from our carefully curated collection of themes, each designed to enhance your writing experience. 
              From the warm tones of Writer's Sanctuary to the deep focus of Midnight Ink.
            </p>
          </div>

          {/* Theme Showcase */}
          <ThemeShowcase />

          {/* Features */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <Palette className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Multiple Themes</h3>
              <p className="text-sm text-muted-foreground">
                Choose from light and dark variants, each with unique color palettes designed for different writing moods.
              </p>
            </div>
            
            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707" />
                </svg>
              </div>
              <h3 className="font-semibold mb-2">System Integration</h3>
              <p className="text-sm text-muted-foreground">
                Automatically adapts to your system preferences or choose your favorite theme manually.
              </p>
            </div>
            
            <div className="text-center p-6 rounded-lg bg-card border">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="font-semibold mb-2">Writer-Focused</h3>
              <p className="text-sm text-muted-foreground">
                Every theme is crafted with writers in mind, optimizing readability and reducing eye strain.
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center">
            <div className="bg-card border rounded-lg p-8 max-w-2xl mx-auto">
              <h3 className="text-xl font-semibold mb-4" style={{ fontFamily: 'Crimson Text, serif' }}>
                Ready to Start Writing?
              </h3>
              <p className="text-muted-foreground mb-6">
                Choose your perfect theme and begin your writing journey with BookScribe AI.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/signup">
                  <Button size="lg" className="w-full sm:w-auto">
                    Get Started Free
                  </Button>
                </Link>
                <Link href="/demo">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Try Demo
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-amber-200/30 dark:border-amber-900/30 bg-white/50 dark:bg-stone-950/50 backdrop-blur-sm mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-sm text-muted-foreground">
            <p>© 2024 BookScribe AI. Crafted for writers, by writers.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
