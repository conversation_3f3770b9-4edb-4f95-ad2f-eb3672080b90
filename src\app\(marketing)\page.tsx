"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { SimpleTypewriter } from "@/components/ui/simple-typewriter";
import { PenTool, BookOpen, Sparkles, Brain, Layers, Zap, Shield, Globe, Palette, Star, ChevronRight, Feather, Coffee, Bookmark, Users, Award, Clock } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 text-gray-900 overflow-hidden">
      {/* Paper texture background */}
      <div className="fixed inset-0 opacity-30" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d4a574' fill-opacity='0.1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3Ccircle cx='37' cy='23' r='1'/%3E%3Ccircle cx='23' cy='37' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }} />
      
      {/* Header */}
      <header className="relative z-50 border-b border-amber-200/30 bg-white/80 backdrop-blur-xl shadow-sm">
        <div className="container flex h-20 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-orange-600 blur-sm opacity-20" />
              <div className="relative w-10 h-10 bg-gradient-to-br from-amber-700 to-orange-700 rounded-lg flex items-center justify-center shadow-md">
                <Feather className="w-6 h-6 text-white" />
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight text-gray-800" style={{ fontFamily: 'Crimson Text, serif' }}>
              BookScribe AI
            </h1>
          </div>

          <nav className="flex gap-2 sm:gap-4">
            <a href="/login">
              <Button
                variant="ghost"
                className="text-gray-700 hover:text-gray-900 hover:bg-amber-100/50 font-medium"
              >
                Sign In
              </Button>
            </a>
            <a href="/signup">
              <Button className="bg-gradient-to-r from-amber-700 to-orange-700 hover:from-amber-800 hover:to-orange-800 text-white shadow-lg font-medium px-6 rounded-xl">
                Begin Writing
                <Feather className="w-4 h-4 ml-1" />
              </Button>
            </a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main id="main-content" className="relative z-10">
        <section className="relative min-h-[90vh] flex items-center justify-center px-4 py-20">
          <div className="container max-w-6xl mx-auto text-center">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-amber-600/30 bg-amber-100/50 mb-8 shadow-sm">
              <BookOpen className="w-4 h-4 text-amber-700" />
              <span className="text-sm text-amber-800 font-medium">Join 10,000+ authors crafting their masterpieces</span>
            </div>

            {/* Main heading with literary styling */}
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 text-gray-800 font-literary-display">
              <span className="block mb-2">
                Your Next Great Novel
              </span>
              <span className="bg-gradient-to-r from-amber-700 via-orange-700 to-red-700 bg-clip-text text-transparent">
                <SimpleTypewriter
                  words={["Awaits Your Pen", "Begins Here", "Starts Today", "Lives Within You"]}
                  typingSpeed={120}
                  deletingSpeed={60}
                  delay={2500}
                />
              </span>
            </h2>

            <p className="text-lg sm:text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8 px-4 font-mono">
              Step into your digital writing sanctuary, where AI meets artistry.
              <span className="block mt-2 text-amber-700 font-semibold">
                From blank page to bestseller, craft stories that captivate.
              </span>
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <a href="/signup">
                <Button
                  size="lg"
                  className="group relative overflow-hidden bg-gradient-to-r from-amber-700 to-orange-700 hover:from-amber-800 hover:to-orange-800 text-white shadow-xl font-medium px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg rounded-xl transition-all duration-300 w-full sm:w-auto border-2 border-amber-800/20"
                >
                  <span className="relative z-10 flex items-center">
                    <Feather className="w-5 h-5 mr-2" />
                    Begin Your Story
                  </span>
                  <div className="absolute inset-0 bg-white/10 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300" />
                </Button>
              </a>
              <a href="/demo">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-amber-600/40 text-amber-800 hover:bg-amber-100/50 hover:border-amber-600/60 font-medium px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg rounded-xl backdrop-blur-sm w-full sm:w-auto bg-white/30"
                >
                  <BookOpen className="w-5 h-5 mr-2" />
                  Explore Features
                </Button>
              </a>
            </div>

            {/* Stats - Literary Style */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center p-6 bg-white/40 rounded-xl border border-amber-200/50 shadow-sm backdrop-blur-sm">
                <div className="text-3xl font-bold text-amber-700 mb-2" style={{ fontFamily: 'Crimson Text, serif' }}>2M+</div>
                <div className="text-sm text-gray-600 font-medium">Words Written Daily</div>
                <div className="w-8 h-1 bg-amber-600/30 mx-auto mt-2 rounded-full"></div>
              </div>
              <div className="text-center p-6 bg-white/40 rounded-xl border border-amber-200/50 shadow-sm backdrop-blur-sm">
                <div className="text-3xl font-bold text-amber-700 mb-2" style={{ fontFamily: 'Crimson Text, serif' }}>98%</div>
                <div className="text-sm text-gray-600 font-medium">Author Satisfaction</div>
                <div className="w-8 h-1 bg-amber-600/30 mx-auto mt-2 rounded-full"></div>
              </div>
              <div className="text-center p-6 bg-white/40 rounded-xl border border-amber-200/50 shadow-sm backdrop-blur-sm">
                <div className="text-3xl font-bold text-amber-700 mb-2" style={{ fontFamily: 'Crimson Text, serif' }}>50+</div>
                <div className="text-sm text-gray-600 font-medium">Published Novels</div>
                <div className="w-8 h-1 bg-amber-600/30 mx-auto mt-2 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Scroll indicator - Literary Style */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-amber-600/30 rounded-full flex justify-center bg-white/20 backdrop-blur-sm">
              <div className="w-1 h-3 bg-amber-600/60 rounded-full mt-2" />
            </div>
          </div>
        </section>

        {/* Features Section - Your Writing Toolkit */}
        <section className="relative py-20 px-4 border-t border-amber-200/30 bg-gradient-to-b from-amber-50/50 to-orange-50/50">
          <div className="container max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h3 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 text-gray-800" style={{ fontFamily: 'Crimson Text, serif' }}>
                Your Writing Toolkit
              </h3>
              <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4" style={{ fontFamily: 'Crimson Text, serif' }}>
                Every tool a novelist needs, beautifully crafted and intelligently designed
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-amber-600 to-orange-600 mx-auto mt-6 rounded-full"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Manuscript Editor */}
              <div className="group relative overflow-hidden rounded-2xl border border-amber-200/50 backdrop-blur-sm bg-white/60 p-8 hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-100/50 via-orange-100/50 to-yellow-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-6 w-16 h-16 rounded-xl bg-gradient-to-br from-amber-600/20 to-orange-600/20 flex items-center justify-center border border-amber-300/30">
                    <PenTool className="w-8 h-8 text-amber-700" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-800 mb-3" style={{ fontFamily: 'Crimson Text, serif' }}>Manuscript Editor</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Write with the elegance of a typewriter and the power of AI. Your words flow naturally while intelligent assistance guides your craft.
                  </p>
                </div>
              </div>

              {/* Story Bible */}
              <div className="group relative overflow-hidden rounded-2xl border border-amber-200/50 backdrop-blur-sm bg-white/60 p-8 hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-100/50 via-orange-100/50 to-yellow-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-6 w-16 h-16 rounded-xl bg-gradient-to-br from-amber-600/20 to-orange-600/20 flex items-center justify-center border border-amber-300/30">
                    <BookOpen className="w-8 h-8 text-amber-700" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-800 mb-3" style={{ fontFamily: 'Crimson Text, serif' }}>Story Bible</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Keep track of every character, location, and plot thread. Your story's universe organized like a master storyteller's notebook.
                  </p>
                </div>
              </div>

              {/* AI Writing Partner */}
              <div className="group relative overflow-hidden rounded-2xl border border-amber-200/50 backdrop-blur-sm bg-white/60 p-8 hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-100/50 via-orange-100/50 to-yellow-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-6 w-16 h-16 rounded-xl bg-gradient-to-br from-amber-600/20 to-orange-600/20 flex items-center justify-center border border-amber-300/30">
                    <Brain className="w-8 h-8 text-amber-700" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-800 mb-3" style={{ fontFamily: 'Crimson Text, serif' }}>AI Writing Partner</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Collaborate with AI that understands your voice and vision. Get suggestions that enhance, never replace, your creative genius.
                  </p>
                </div>
              </div>

              {/* Character Development */}
              <div className="group relative overflow-hidden rounded-2xl border border-amber-200/50 backdrop-blur-sm bg-white/60 p-8 hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-100/50 via-orange-100/50 to-yellow-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-6 w-16 h-16 rounded-xl bg-gradient-to-br from-amber-600/20 to-orange-600/20 flex items-center justify-center border border-amber-300/30">
                    <Users className="w-8 h-8 text-amber-700" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-800 mb-3" style={{ fontFamily: 'Crimson Text, serif' }}>Character Development</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Breathe life into your characters with detailed profiles, relationship maps, and personality insights that evolve with your story.
                  </p>
                </div>
              </div>

              {/* Progress Tracking */}
              <div className="group relative overflow-hidden rounded-2xl border border-amber-200/50 backdrop-blur-sm bg-white/60 p-8 hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-100/50 via-orange-100/50 to-yellow-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-6 w-16 h-16 rounded-xl bg-gradient-to-br from-amber-600/20 to-orange-600/20 flex items-center justify-center border border-amber-300/30">
                    <Award className="w-8 h-8 text-amber-700" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-800 mb-3" style={{ fontFamily: 'Crimson Text, serif' }}>Progress Tracking</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Watch your novel grow with beautiful visualizations, milestone celebrations, and insights that keep you motivated.
                  </p>
                </div>
              </div>

              {/* Research Hub */}
              <div className="group relative overflow-hidden rounded-2xl border border-amber-200/50 backdrop-blur-sm bg-white/60 p-8 hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-100/50 via-orange-100/50 to-yellow-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-6 w-16 h-16 rounded-xl bg-gradient-to-br from-amber-600/20 to-orange-600/20 flex items-center justify-center border border-amber-300/30">
                    <Globe className="w-8 h-8 text-amber-700" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-800 mb-3" style={{ fontFamily: 'Crimson Text, serif' }}>Research Hub</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Organize your research, inspiration, and reference materials in one accessible place, always at your fingertips while you write.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Testimonials Section */}
        <section className="relative py-20 px-4 bg-gradient-to-b from-orange-50/50 to-amber-100/50 border-t border-amber-200/30">
          <div className="container max-w-4xl mx-auto text-center">
            <h3 className="text-3xl sm:text-4xl font-bold mb-4 text-gray-800" style={{ fontFamily: 'Crimson Text, serif' }}>
              Loved by Authors Worldwide
            </h3>
            <div className="w-24 h-1 bg-gradient-to-r from-amber-600 to-orange-600 mx-auto mb-12 rounded-full"></div>

            <div className="bg-white/60 rounded-2xl p-8 border border-amber-200/50 shadow-lg backdrop-blur-sm">
              <div className="flex justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-amber-500 fill-current" />
                ))}
              </div>
              <blockquote className="text-lg text-gray-700 mb-6 italic" style={{ fontFamily: 'Crimson Text, serif' }}>
                "BookScribe AI transformed my writing process. What used to take months now flows naturally in weeks.
                The AI understands my voice and helps me craft stories I never thought possible."
              </blockquote>
              <div className="flex items-center justify-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-amber-600 to-orange-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">SJ</span>
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-800">Sarah Johnson</div>
                  <div className="text-sm text-gray-600">Author of "The Midnight Chronicles"</div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="relative z-10 border-t border-amber-200/30 py-12 bg-gradient-to-b from-amber-100/30 to-orange-100/30 backdrop-blur-xl">
        <div className="container text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-amber-700 to-orange-700 rounded-lg flex items-center justify-center shadow-md">
              <Feather className="w-4 h-4 text-white" />
            </div>
            <span className="text-lg font-semibold text-gray-800" style={{ fontFamily: 'Crimson Text, serif' }}>
              BookScribe AI
            </span>
          </div>
          <p className="text-gray-600 text-sm">
            © 2025 BookScribe AI. Where every great story begins.
          </p>
        </div>
      </footer>
    </div>
  );
}