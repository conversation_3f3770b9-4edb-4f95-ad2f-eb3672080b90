import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import * as epub from 'epub'
import { promisify } from 'util'

interface ParsedChapter {
  title: string
  content: string
  wordCount: number
  chapterNumber: number
}

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const projectId = formData.get('projectId') as string

    if (!file || !projectId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Convert file to buffer and save temporarily
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    
    // Create temporary file path
    const tempPath = `/tmp/${Date.now()}_${file.name}`
    const fs = await import('fs').then(m => m.promises)
    await fs.writeFile(tempPath, buffer)

    try {
      // Parse EPUB
      const epubBook = new epub(tempPath)
      await new Promise((resolve, reject) => {
        epubBook.on('end', resolve)
        epubBook.on('error', reject)
        epubBook.parse()
      })

      // Extract chapters
      const chapters: ParsedChapter[] = []
      let chapterNumber = 0

      // Get chapter list from spine
      for (const spineItem of epubBook.flow) {
        chapterNumber++
        
        // Get chapter content
        const getChapter = promisify(epubBook.getChapter.bind(epubBook))
        const chapterHtml = await getChapter(spineItem.id)
        
        // Extract text from HTML
        const text = extractTextFromHtml(chapterHtml)
        
        // Get title from TOC or use default
        const tocItem = epubBook.toc.find(item => item.href === spineItem.href)
        const title = tocItem?.title || `Chapter ${chapterNumber}`
        
        if (text.trim()) {
          chapters.push({
            title,
            content: text,
            wordCount: text.split(/\s+/).filter(word => word.length > 0).length,
            chapterNumber
          })
        }
      }

      // Clean up temp file
      await fs.unlink(tempPath)

      // Create chapters in database
      const supabase = createClient()
      const createdChapters = []

      for (const chapter of chapters) {
        const { data, error } = await supabase
          .from('chapters')
          .insert({
            project_id: projectId,
            chapter_number: chapter.chapterNumber,
            title: chapter.title,
            content: chapter.content,
            actual_word_count: chapter.wordCount,
            status: 'imported',
            created_by: authResult.user!.id
          })
          .select()
          .single()

        if (error) {
          console.error('Error creating chapter:', error)
          continue
        }

        createdChapters.push(data)
      }

      // Update project metadata
      const totalWordCount = chapters.reduce((sum, ch) => sum + ch.wordCount, 0)
      
      // Get book metadata
      const metadata = {
        title: epubBook.metadata.title,
        author: epubBook.metadata.creator,
        publisher: epubBook.metadata.publisher,
        language: epubBook.metadata.language,
        description: epubBook.metadata.description
      }

      await supabase
        .from('projects')
        .update({
          word_count: totalWordCount,
          chapters_count: chapters.length,
          metadata: metadata,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId)

      return NextResponse.json({
        success: true,
        chaptersImported: createdChapters.length,
        totalWordCount,
        metadata,
        chapters: createdChapters.map(ch => ({
          id: ch.id,
          title: ch.title,
          chapterNumber: ch.chapter_number,
          wordCount: ch.actual_word_count
        }))
      })

    } catch (parseError) {
      // Clean up temp file on error
      await fs.unlink(tempPath).catch(() => {})
      throw parseError
    }

  } catch (error) {
    return handleRouteError(error, 'EPUB Import')
  }
}

function extractTextFromHtml(html: string): string {
  // Remove HTML tags and decode entities
  const text = html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
    .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
    .replace(/<[^>]+>/g, ' ') // Remove HTML tags
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()

  return text
}