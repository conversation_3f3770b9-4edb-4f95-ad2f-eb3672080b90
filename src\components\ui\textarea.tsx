import * as React from "react"
import { cn } from "@/lib/utils"

export type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          "flex min-h-[100px] w-full rounded-xl border-2 border-warm-200 bg-warm-50/50 px-4 py-3 text-base ring-offset-background placeholder:text-warm-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-literary-amber focus-visible:ring-offset-2 focus-visible:border-literary-amber disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 md:text-sm backdrop-blur-sm font-literary resize-none",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }