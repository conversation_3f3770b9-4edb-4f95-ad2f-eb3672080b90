@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import editor layout animations */
@import '../styles/editor-layout.css';

@layer base {
  :root {
    /* Writer's Sanctuary Theme - Warm Literary Colors */
    --background: 45 50% 97%; /* Warm paper white */
    --foreground: 25 25% 15%; /* Dark charcoal text */
    --card: 42 45% 95%; /* Slightly warmer card background */
    --card-foreground: 25 25% 15%;
    --popover: 42 45% 95%;
    --popover-foreground: 25 25% 15%;
    --primary: 25 75% 45%; /* Rich amber-brown */
    --primary-foreground: 45 50% 97%;
    --secondary: 40 35% 88%; /* Warm beige */
    --secondary-foreground: 25 25% 15%;
    --muted: 40 30% 90%; /* Light warm gray */
    --muted-foreground: 25 15% 45%; /* Medium warm gray */
    --accent: 35 60% 85%; /* Light amber accent */
    --accent-foreground: 25 25% 15%;
    --destructive: 0 75% 55%; /* Warm red */
    --destructive-foreground: 45 50% 97%;
    --border: 40 25% 85%; /* Warm border */
    --input: 40 25% 90%; /* Input background */
    --ring: 25 75% 45%; /* Focus ring matches primary */
    --radius: 0.75rem; /* Slightly more rounded for warmth */

    /* Literary-specific colors */
    --literary-gold: 45 85% 55%;
    --literary-amber: 35 75% 50%;
    --literary-parchment: 45 40% 92%;
    --literary-ink: 25 35% 20%;
    --literary-quill: 30 45% 35%;
  }

  .dark {
    /* Dark Writer's Sanctuary Theme - Evening Study */
    --background: 25 15% 8%; /* Deep warm dark */
    --foreground: 45 25% 90%; /* Warm light text */
    --card: 25 20% 12%; /* Slightly lighter card */
    --card-foreground: 45 25% 90%;
    --popover: 25 20% 12%;
    --popover-foreground: 45 25% 90%;
    --primary: 45 85% 65%; /* Bright warm amber */
    --primary-foreground: 25 15% 8%;
    --secondary: 25 15% 18%; /* Dark warm secondary */
    --secondary-foreground: 45 25% 90%;
    --muted: 25 15% 15%; /* Dark muted */
    --muted-foreground: 45 15% 65%; /* Medium warm gray */
    --accent: 35 45% 25%; /* Dark amber accent */
    --accent-foreground: 45 25% 90%;
    --destructive: 0 65% 55%; /* Warm red */
    --destructive-foreground: 45 25% 90%;
    --border: 25 15% 20%; /* Dark warm border */
    --input: 25 15% 15%; /* Dark input */
    --ring: 45 85% 65%; /* Bright focus ring */

    /* Dark literary colors */
    --literary-gold: 45 85% 65%;
    --literary-amber: 35 75% 60%;
    --literary-parchment: 25 20% 15%;
    --literary-ink: 45 25% 85%;
    --literary-quill: 35 35% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.6;
  }

  /* Typography system */
  .font-literary {
    font-family: 'Crimson Text', Georgia, serif;
  }

  .font-literary-display {
    font-family: 'Crimson Text', Georgia, serif;
    font-weight: 600;
    letter-spacing: -0.025em;
  }
  
  /* Standardized font sizes */
  .text-xs { font-size: 12px !important; line-height: 1.5; }
  .text-sm { font-size: 14px !important; line-height: 1.5; }
  .text-base { font-size: 16px !important; line-height: 1.6; }
  .text-lg { font-size: 18px !important; line-height: 1.6; }
  .text-xl { font-size: 20px !important; line-height: 1.4; }
  .text-2xl { font-size: 24px !important; line-height: 1.3; }
  .text-3xl { font-size: 30px !important; line-height: 1.2; }
  .text-4xl { font-size: 36px !important; line-height: 1.2; }
  
  .font-mono {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
  }
  
  /* Headers - use serif font */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Crimson Text', Georgia, serif;
    font-weight: 600;
    letter-spacing: -0.025em;
    margin-bottom: 1rem;
  }
  
  h1 { font-size: 36px; line-height: 1.2; }
  h2 { font-size: 30px; line-height: 1.3; }
  h3 { font-size: 24px; line-height: 1.4; }
  h4 { font-size: 20px; line-height: 1.4; }
  h5 { font-size: 18px; line-height: 1.5; }
  h6 { font-size: 16px; line-height: 1.5; }
  
  /* Form inputs, code, and body text - use monospace */
  input, textarea, select, code, pre {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
  }
  
  /* Labels and small text */
  label, .text-xs, .text-sm {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-weight: 400;
  }
  
  /* Paragraphs and body text */
  p, li, td, dd {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
  }
  
  /* Navigation and menu items */
  nav, .menu-item, [role="menuitem"] {
    font-family: 'Crimson Text', Georgia, serif;
    font-weight: 500;
  }
  
  /* Dialog and modal titles */
  [role="dialog"] h2, [role="dialog"] h3,
  .modal-title, .dialog-title {
    font-family: 'Crimson Text', Georgia, serif;
    font-weight: 600;
  }
  
  /* Important UI elements */
  .badge, .chip, .tag {
    font-family: 'Crimson Text', Georgia, serif;
  }
  
  /* Prevent text cutoff */
  * {
    overflow-wrap: break-word;
    word-wrap: break-word;
  }
  
  /* Consistent border radius */
  .rounded-xs { border-radius: 0.25rem; }
  .rounded-sm { border-radius: 0.375rem; }
  .rounded-md { border-radius: 0.5rem; }
  .rounded-lg { border-radius: 0.75rem; }
  .rounded-xl { border-radius: 1rem; }
  .rounded-2xl { border-radius: 1.5rem; }

  /* Paper texture background */
  .paper-texture {
    background-image:
      radial-gradient(circle at 1px 1px, rgba(180, 140, 90, 0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }
  
  /* Consistent spacing */
  .space-y-comfortable > * + * {
    margin-top: 1.5rem;
  }
  
  /* Better scroll containers */
  .scroll-container {
    @apply overflow-y-auto overflow-x-hidden;
    scrollbar-width: thin;
    scrollbar-color: theme('colors.warm.300') transparent;
  }
  
  .scroll-container::-webkit-scrollbar {
    width: 8px;
  }
  
  .scroll-container::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scroll-container::-webkit-scrollbar-thumb {
    @apply bg-warm-300 rounded-full;
  }
  
  .scroll-container::-webkit-scrollbar-thumb:hover {
    @apply bg-warm-400;
  }

  /* Ink drop effect for buttons */
  .ink-drop {
    position: relative;
    overflow: hidden;
  }

  .ink-drop::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .ink-drop:hover::before {
    width: 300px;
    height: 300px;
  }
}

/* Modern gradient animations */
@layer utilities {
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 8s ease infinite;
  }
  
  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  /* Noise texture effect */
  .noise-texture {
    position: relative;
  }
  
  .noise-texture::before {
    content: "";
    position: absolute;
    inset: 0;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
    opacity: 0.02;
    mix-blend-mode: overlay;
    pointer-events: none;
  }
}

/* Tutorial Highlighting */
.tutorial-highlight {
  position: relative;
  z-index: 35;
  outline: 3px solid hsl(var(--primary));
  outline-offset: 4px;
  border-radius: 0.375rem;
  animation: tutorial-pulse 2s ease-in-out infinite;
}

@keyframes tutorial-pulse {
  0%, 100% {
    outline-color: hsl(var(--primary));
    outline-offset: 4px;
  }
  50% {
    outline-color: hsl(var(--primary) / 0.6);
    outline-offset: 8px;
  }
}

/* Accessibility - Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus Visible Improvements */
*:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Better Mobile Tap Targets */
@media (max-width: 768px) {
  button, a, input, textarea, select {
    min-height: 44px;
    min-width: 44px;
  }
}
