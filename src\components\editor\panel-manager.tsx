'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { toast } from '@/hooks/use-toast'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  Layout,
  Save,
  RotateCcw,
  Maximize2,
  Minimize2,
  Pin,
  BookOpen,
  MessageSquare,
  History,
  FileText,
  BarChart3,
  Users,
  Brain
} from 'lucide-react'

export interface PanelConfig {
  id: string
  label: string
  icon: React.ElementType
  visible: boolean
  pinned: boolean
  position: 'left' | 'right' | 'bottom' | 'floating'
  width?: number
  height?: number
  x?: number
  y?: number
}

export interface LayoutPreset {
  id: string
  name: string
  description: string
  panels: PanelConfig[]
}

interface PanelManagerProps {
  currentPanels: PanelConfig[]
  onPanelToggle: (panelId: string) => void
  onPanelPin: (panelId: string) => void
  onLayoutChange: (panels: PanelConfig[]) => void
  onPresetSelect: (preset: LayoutPreset) => void
}

const defaultPresets: LayoutPreset[] = [
  {
    id: 'writing',
    name: 'Writing Mode',
    description: 'Focus on writing with minimal distractions',
    panels: [
      { id: 'chapters', label: 'Chapters', icon: FileText, visible: true, pinned: true, position: 'left' },
      { id: 'knowledge', label: 'Knowledge', icon: Brain, visible: true, pinned: false, position: 'right' },
      { id: 'ai-chat', label: 'AI Chat', icon: MessageSquare, visible: false, pinned: false, position: 'right' },
      { id: 'story-bible', label: 'Story Bible', icon: BookOpen, visible: false, pinned: false, position: 'right' },
      { id: 'stats', label: 'Writing Stats', icon: BarChart3, visible: false, pinned: false, position: 'bottom' }
    ]
  },
  {
    id: 'editing',
    name: 'Editing Mode',
    description: 'All tools visible for comprehensive editing',
    panels: [
      { id: 'chapters', label: 'Chapters', icon: FileText, visible: true, pinned: true, position: 'left' },
      { id: 'knowledge', label: 'Knowledge', icon: Brain, visible: true, pinned: true, position: 'right' },
      { id: 'ai-chat', label: 'AI Chat', icon: MessageSquare, visible: true, pinned: false, position: 'right' },
      { id: 'story-bible', label: 'Story Bible', icon: BookOpen, visible: true, pinned: false, position: 'right' },
      { id: 'stats', label: 'Writing Stats', icon: BarChart3, visible: true, pinned: false, position: 'bottom' }
    ]
  },
  {
    id: 'planning',
    name: 'Planning Mode',
    description: 'Focus on story structure and world-building',
    panels: [
      { id: 'chapters', label: 'Chapters', icon: FileText, visible: true, pinned: false, position: 'left' },
      { id: 'knowledge', label: 'Knowledge', icon: Brain, visible: false, pinned: false, position: 'right' },
      { id: 'ai-chat', label: 'AI Chat', icon: MessageSquare, visible: true, pinned: true, position: 'right' },
      { id: 'story-bible', label: 'Story Bible', icon: BookOpen, visible: true, pinned: true, position: 'right' },
      { id: 'stats', label: 'Writing Stats', icon: BarChart3, visible: false, pinned: false, position: 'bottom' }
    ]
  },
  {
    id: 'minimal',
    name: 'Minimal Mode',
    description: 'Just the editor - distraction free',
    panels: [
      { id: 'chapters', label: 'Chapters', icon: FileText, visible: false, pinned: false, position: 'left' },
      { id: 'knowledge', label: 'Knowledge', icon: Brain, visible: false, pinned: false, position: 'right' },
      { id: 'ai-chat', label: 'AI Chat', icon: MessageSquare, visible: false, pinned: false, position: 'right' },
      { id: 'story-bible', label: 'Story Bible', icon: BookOpen, visible: false, pinned: false, position: 'right' },
      { id: 'stats', label: 'Writing Stats', icon: BarChart3, visible: false, pinned: false, position: 'bottom' }
    ]
  }
]

export function PanelManager({
  currentPanels,
  onPanelToggle,
  onPanelPin,
  onLayoutChange,
  onPresetSelect
}: PanelManagerProps) {
  const [customLayouts, setCustomLayouts] = useState<LayoutPreset[]>([])
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Load custom layouts from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('bookscribe-custom-layouts')
    if (saved) {
      setCustomLayouts(JSON.parse(saved))
    }
  }, [])

  const saveCurrentLayout = () => {
    const layoutName = prompt('Enter a name for this layout:')
    if (!layoutName) return

    const newLayout: LayoutPreset = {
      id: `custom-${Date.now()}`,
      name: layoutName,
      description: 'Custom layout',
      panels: currentPanels
    }

    const updated = [...customLayouts, newLayout]
    setCustomLayouts(updated)
    localStorage.setItem('bookscribe-custom-layouts', JSON.stringify(updated))
    
    toast({
      title: 'Layout saved',
      description: `Layout "${layoutName}" has been saved`
    })
  }

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  const visiblePanels = currentPanels.filter(p => p.visible).length

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Layout className="h-4 w-4" />
          Layout
          {visiblePanels > 0 && (
            <Badge variant="secondary" className="ml-1 h-5 px-1">
              {visiblePanels}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel>Panel Layout</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* Quick Presets */}
        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Quick Presets
        </DropdownMenuLabel>
        {defaultPresets.map(preset => (
          <DropdownMenuItem
            key={preset.id}
            onClick={() => onPresetSelect(preset)}
            className="flex flex-col items-start py-2"
          >
            <div className="font-medium text-sm">{preset.name}</div>
            <div className="text-xs text-muted-foreground">{preset.description}</div>
          </DropdownMenuItem>
        ))}
        
        {customLayouts.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Custom Layouts
            </DropdownMenuLabel>
            {customLayouts.map(layout => (
              <DropdownMenuItem
                key={layout.id}
                onClick={() => onPresetSelect(layout)}
              >
                {layout.name}
              </DropdownMenuItem>
            ))}
          </>
        )}
        
        <DropdownMenuSeparator />
        
        {/* Panel Toggles */}
        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Toggle Panels
        </DropdownMenuLabel>
        {currentPanels.map(panel => {
          const Icon = panel.icon
          return (
            <DropdownMenuCheckboxItem
              key={panel.id}
              checked={panel.visible}
              onCheckedChange={() => onPanelToggle(panel.id)}
            >
              <Icon className="h-4 w-4 mr-2" />
              {panel.label}
              {panel.pinned && <Pin className="h-3 w-3 ml-auto" />}
            </DropdownMenuCheckboxItem>
          )
        })}
        
        <DropdownMenuSeparator />
        
        {/* Actions */}
        <DropdownMenuItem onClick={saveCurrentLayout}>
          <Save className="h-4 w-4 mr-2" />
          Save Current Layout
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => onLayoutChange(defaultPresets[0].panels)}>
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset to Default
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={toggleFullscreen}>
          {isFullscreen ? (
            <>
              <Minimize2 className="h-4 w-4 mr-2" />
              Exit Fullscreen
            </>
          ) : (
            <>
              <Maximize2 className="h-4 w-4 mr-2" />
              Enter Fullscreen
            </>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Helper function to apply a preset
export function applyLayoutPreset(preset: LayoutPreset, callbacks: {
  toggleChapterNavigator: () => void
  toggleStoryBible: () => void
  toggleAiChat: () => void
  toggleVersionHistory: () => void
  setShowKnowledgePanel: (show: boolean) => void
  setShowWritingStats: (show: boolean) => void
}) {
  preset.panels.forEach(panel => {
    switch (panel.id) {
      case 'chapters':
        if (panel.visible !== callbacks.toggleChapterNavigator) {
          callbacks.toggleChapterNavigator()
        }
        break
      case 'story-bible':
        if (panel.visible !== callbacks.toggleStoryBible) {
          callbacks.toggleStoryBible()
        }
        break
      case 'ai-chat':
        if (panel.visible !== callbacks.toggleAiChat) {
          callbacks.toggleAiChat()
        }
        break
      case 'version-history':
        if (panel.visible !== callbacks.toggleVersionHistory) {
          callbacks.toggleVersionHistory()
        }
        break
      case 'knowledge':
        callbacks.setShowKnowledgePanel(panel.visible)
        break
      case 'stats':
        callbacks.setShowWritingStats(panel.visible)
        break
    }
  })
}