'use client'

import React, { useState, useCallback, useEffect, useRef } from 'react'

interface UseResizablePanelProps {
  initialWidth?: number
  minWidth?: number
  maxWidth?: number
  onResize?: (width: number) => void
}

export function useResizablePanel({
  initialWidth = 384, // w-96 equivalent
  minWidth = 280,
  maxWidth = 600,
  onResize
}: UseResizablePanelProps = {}) {
  const [width, setWidth] = useState(initialWidth)
  const [isResizing, setIsResizing] = useState(false)
  const startXRef = useRef(0)
  const startWidthRef = useRef(initialWidth)

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsResizing(true)
    startXRef.current = e.clientX
    startWidthRef.current = width
    
    // Add cursor style to body
    document.body.style.cursor = 'col-resize'
    document.body.style.userSelect = 'none'
  }, [width])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return

    const deltaX = startXRef.current - e.clientX // Reversed for right-side panel
    const newWidth = Math.min(
      Math.max(startWidthRef.current + deltaX, minWidth),
      maxWidth
    )
    
    setWidth(newWidth)
    onResize?.(newWidth)
  }, [isResizing, minWidth, maxWidth, onResize])

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }, [])

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isResizing, handleMouseMove, handleMouseUp])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [])

  const ResizeHandle = () => (
    <div
      className={`
        absolute left-0 top-0 bottom-0 w-2 cursor-col-resize 
        hover:bg-primary/30 transition-colors duration-200
        ${isResizing ? 'bg-primary/50' : 'bg-border'}
        group z-50
      `}
      onMouseDown={handleMouseDown}
    >
      <div className="absolute left-0 top-0 bottom-0 w-6 -translate-x-2" />
      <div className={`
        absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-1 h-12 
        bg-primary rounded-full opacity-0 group-hover:opacity-100 
        transition-opacity duration-200
        ${isResizing ? 'opacity-100' : ''}
      `} />
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col gap-1">
        <div className="w-1 h-1 bg-primary rounded-full opacity-0 group-hover:opacity-60 transition-opacity" />
        <div className="w-1 h-1 bg-primary rounded-full opacity-0 group-hover:opacity-60 transition-opacity" />
        <div className="w-1 h-1 bg-primary rounded-full opacity-0 group-hover:opacity-60 transition-opacity" />
      </div>
    </div>
  )

  return {
    width,
    isResizing,
    ResizeHandle,
    setWidth
  }
}
