/**
 * Theme Showcase Component
 * Displays all available themes with preview cards
 */

'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { getAllThemes, getThemesByMode } from '@/lib/themes/theme-registry'
import { useBookScribeTheme } from '@/hooks/use-bookscribe-theme'
import { Palette, Sun, Moon, Check } from 'lucide-react'

interface ThemePreviewProps {
  themeId: string
  themeName: string
  themeDescription: string
  themeMode: 'light' | 'dark'
  colors: {
    background: string
    text: string
    accent: string
    card: string
  }
  isActive: boolean
  onSelect: () => void
}

function ThemePreview({ 
  themeId, 
  themeName, 
  themeDescription, 
  themeMode, 
  colors, 
  isActive, 
  onSelect 
}: ThemePreviewProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
        isActive ? 'ring-2 ring-primary' : ''
      }`}
      onClick={onSelect}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {themeMode === 'light' ? (
              <Sun className="h-4 w-4 text-amber-600" />
            ) : (
              <Moon className="h-4 w-4 text-blue-400" />
            )}
            <CardTitle className="text-sm font-medium">{themeName}</CardTitle>
          </div>
          {isActive && (
            <Check className="h-4 w-4 text-primary" />
          )}
        </div>
        <CardDescription className="text-xs">{themeDescription}</CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Color Preview */}
        <div 
          className="rounded-lg p-4 mb-3 border"
          style={{ 
            backgroundColor: colors.background,
            borderColor: colors.accent + '40'
          }}
        >
          <div 
            className="text-xs font-medium mb-2"
            style={{ color: colors.text }}
          >
            Sample Text
          </div>
          <div className="flex gap-1">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors.accent }}
            />
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors.card }}
            />
            <div 
              className="w-3 h-3 rounded-full opacity-60"
              style={{ backgroundColor: colors.text }}
            />
          </div>
        </div>
        
        <Badge variant={themeMode === 'light' ? 'default' : 'secondary'} className="text-xs">
          {themeMode}
        </Badge>
      </CardContent>
    </Card>
  )
}

export function ThemeShowcase() {
  const { switchToTheme, isThemeActive, mounted } = useBookScribeTheme()

  if (!mounted) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-3/4" />
              <div className="h-3 bg-muted rounded w-full" />
            </CardHeader>
            <CardContent>
              <div className="h-16 bg-muted rounded mb-3" />
              <div className="h-5 bg-muted rounded w-16" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const lightThemes = getThemesByMode('light')
  const darkThemes = getThemesByMode('dark')

  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Palette className="h-5 w-5 text-primary" />
          <h2 className="text-2xl font-bold">Theme Gallery</h2>
        </div>
        <p className="text-muted-foreground">
          Choose from our collection of carefully crafted writing themes
        </p>
      </div>

      {/* Light Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Sun className="h-4 w-4 text-amber-600" />
          <h3 className="text-lg font-semibold">Light Themes</h3>
          <Badge variant="outline">{lightThemes.length}</Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {lightThemes.map((theme) => (
            <ThemePreview
              key={theme.id}
              themeId={theme.id}
              themeName={theme.name}
              themeDescription={theme.description}
              themeMode={theme.mode}
              colors={{
                background: theme.colors.background,
                text: theme.colors.text,
                accent: theme.colors.accent,
                card: theme.colors.window
              }}
              isActive={isThemeActive(theme.id)}
              onSelect={() => switchToTheme(theme.id)}
            />
          ))}
        </div>
      </div>

      {/* Dark Themes */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Moon className="h-4 w-4 text-blue-400" />
          <h3 className="text-lg font-semibold">Dark Themes</h3>
          <Badge variant="outline">{darkThemes.length}</Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {darkThemes.map((theme) => (
            <ThemePreview
              key={theme.id}
              themeId={theme.id}
              themeName={theme.name}
              themeDescription={theme.description}
              themeMode={theme.mode}
              colors={{
                background: theme.colors.background,
                text: theme.colors.text,
                accent: theme.colors.accent,
                card: theme.colors.window
              }}
              isActive={isThemeActive(theme.id)}
              onSelect={() => switchToTheme(theme.id)}
            />
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-2 justify-center pt-4 border-t">
        <Button
          variant="outline"
          size="sm"
          onClick={() => switchToTheme('writers-sanctuary-light')}
        >
          <Sun className="h-3 w-3 mr-1" />
          Default Light
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => switchToTheme('evening-study-dark')}
        >
          <Moon className="h-3 w-3 mr-1" />
          Default Dark
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => switchToTheme('system')}
        >
          <Palette className="h-3 w-3 mr-1" />
          System
        </Button>
      </div>
    </div>
  )
}
