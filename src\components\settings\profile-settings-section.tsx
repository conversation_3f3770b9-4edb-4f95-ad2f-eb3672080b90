'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  User, 
  Mail, 
  Calendar, 
  MapPin, 
  Globe, 
  Edit3, 
  Camera, 
  Shield, 
  Bell,
  LogOut,
  Trash2,
  Download,
  Upload,
  Eye,
  EyeOff,
  CheckCircle2,
  AlertCircle,
  Coffee,
  Feather
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { createClient } from '@/lib/supabase/client';
import { toast } from '@/hooks/use-toast';

interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  username?: string;
  bio?: string;
  location?: string;
  website?: string;
  avatar_url?: string;
  writing_goals?: {
    daily_words?: number;
    weekly_hours?: number;
    genre_focus?: string;
  };
  preferences?: {
    public_profile?: boolean;
    email_notifications?: boolean;
    writing_reminders?: boolean;
    beta_features?: boolean;
  };
  stats?: {
    projects_count?: number;
    total_words?: number;
    days_active?: number;
    member_since?: string;
  };
}

export function ProfileSettingsSection() {
  const { user, signOut } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [formData, setFormData] = useState({
    full_name: '',
    username: '',
    bio: '',
    location: '',
    website: '',
    daily_words: 1000,
    weekly_hours: 10,
    genre_focus: '',
    public_profile: true,
    email_notifications: true,
    writing_reminders: true,
    beta_features: false,
  });

  const supabase = createClient();

  // Load user profile
  useEffect(() => {
    if (user) {
      loadUserProfile();
    }
  }, [user]);

  const loadUserProfile = async () => {
    if (!user) return;

    try {
      // Get profile data from profiles table (if it exists)
      const { data: profileData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // Get project stats
      const { data: projectStats } = await supabase
        .from('projects')
        .select('id, current_word_count, created_at')
        .eq('user_id', user.id);

      const totalWords = projectStats?.reduce((sum, p) => sum + (p.current_word_count || 0), 0) || 0;
      const projectsCount = projectStats?.length || 0;
      const memberSince = user.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown';

      const userProfile: UserProfile = {
        id: user.id,
        email: user.email || '',
        full_name: profileData?.full_name || user.user_metadata?.full_name || '',
        username: profileData?.username || '',
        bio: profileData?.bio || '',
        location: profileData?.location || '',
        website: profileData?.website || '',
        avatar_url: profileData?.avatar_url || user.user_metadata?.avatar_url || '',
        writing_goals: profileData?.writing_goals || {
          daily_words: 1000,
          weekly_hours: 10,
          genre_focus: 'Fiction'
        },
        preferences: profileData?.preferences || {
          public_profile: true,
          email_notifications: true,
          writing_reminders: true,
          beta_features: false
        },
        stats: {
          projects_count: projectsCount,
          total_words: totalWords,
          days_active: 0, // Could calculate from activity logs
          member_since: memberSince
        }
      };

      setProfile(userProfile);
      setFormData({
        full_name: userProfile.full_name || '',
        username: userProfile.username || '',
        bio: userProfile.bio || '',
        location: userProfile.location || '',
        website: userProfile.website || '',
        daily_words: userProfile.writing_goals?.daily_words || 1000,
        weekly_hours: userProfile.writing_goals?.weekly_hours || 10,
        genre_focus: userProfile.writing_goals?.genre_focus || 'Fiction',
        public_profile: userProfile.preferences?.public_profile ?? true,
        email_notifications: userProfile.preferences?.email_notifications ?? true,
        writing_reminders: userProfile.preferences?.writing_reminders ?? true,
        beta_features: userProfile.preferences?.beta_features ?? false,
      });
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  const handleSaveProfile = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const profileUpdate = {
        id: user.id,
        full_name: formData.full_name,
        username: formData.username,
        bio: formData.bio,
        location: formData.location,
        website: formData.website,
        writing_goals: {
          daily_words: formData.daily_words,
          weekly_hours: formData.weekly_hours,
          genre_focus: formData.genre_focus
        },
        preferences: {
          public_profile: formData.public_profile,
          email_notifications: formData.email_notifications,
          writing_reminders: formData.writing_reminders,
          beta_features: formData.beta_features
        },
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('profiles')
        .upsert(profileUpdate);

      if (error) throw error;

      await loadUserProfile();
      setIsEditing(false);
      toast({
        title: 'Profile Updated',
        description: 'Your profile has been saved successfully.',
      });
    } catch (error) {
      console.error('Error saving profile:', error);
      toast({
        title: 'Save Failed',
        description: 'There was an error saving your profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: 'Signed Out',
        description: 'You have been signed out successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error signing out.',
        variant: 'destructive',
      });
    }
  };

  const getUserInitials = (name: string | undefined, email: string | undefined) => {
    if (name) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return 'U';
  };

  if (!user) {
    return (
      <div className="text-center py-12">
        <div className="mb-6">
          <User className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Sign In Required</h3>
          <p className="text-muted-foreground mb-6">
            Please sign in to access your profile settings.
          </p>
        </div>
        <div className="flex gap-4 justify-center">
          <Button asChild>
            <a href="/login">Sign In</a>
          </Button>
          <Button variant="outline" asChild>
            <a href="/signup">Create Account</a>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Manage your account details and writing preferences
              </CardDescription>
            </div>
            <Button
              variant={isEditing ? "default" : "outline"}
              onClick={() => setIsEditing(!isEditing)}
              disabled={isLoading}
            >
              <Edit3 className="w-4 h-4 mr-2" />
              {isEditing ? 'Cancel' : 'Edit Profile'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-6">
            {/* Avatar */}
            <div className="flex flex-col items-center gap-3">
              <Avatar className="w-24 h-24">
                <AvatarImage src={profile?.avatar_url} />
                <AvatarFallback className="text-lg">
                  {getUserInitials(profile?.full_name, profile?.email)}
                </AvatarFallback>
              </Avatar>
              {isEditing && (
                <Button variant="outline" size="sm" disabled>
                  <Camera className="w-4 h-4 mr-2" />
                  Change Photo
                </Button>
              )}
            </div>

            {/* Profile Details */}
            <div className="flex-1 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="full_name">Full Name</Label>
                  {isEditing ? (
                    <Input
                      id="full_name"
                      value={formData.full_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                      placeholder="Enter your full name"
                    />
                  ) : (
                    <div className="text-sm text-muted-foreground mt-1">
                      {profile?.full_name || 'Not set'}
                    </div>
                  )}
                </div>
                <div>
                  <Label htmlFor="username">Username</Label>
                  {isEditing ? (
                    <Input
                      id="username"
                      value={formData.username}
                      onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                      placeholder="Choose a username"
                    />
                  ) : (
                    <div className="text-sm text-muted-foreground mt-1">
                      {profile?.username || 'Not set'}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="email">Email</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Mail className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">{profile?.email}</span>
                  <Badge variant="outline" className="text-xs">
                    <CheckCircle2 className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                </div>
              </div>

              <div>
                <Label htmlFor="bio">Bio</Label>
                {isEditing ? (
                  <Textarea
                    id="bio"
                    value={formData.bio}
                    onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                    placeholder="Tell us about yourself and your writing..."
                    rows={3}
                  />
                ) : (
                  <div className="text-sm text-muted-foreground mt-1">
                    {profile?.bio || 'No bio added yet'}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="location">Location</Label>
                  {isEditing ? (
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                      placeholder="City, Country"
                    />
                  ) : (
                    <div className="flex items-center gap-2 mt-1">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{profile?.location || 'Not set'}</span>
                    </div>
                  )}
                </div>
                <div>
                  <Label htmlFor="website">Website</Label>
                  {isEditing ? (
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                      placeholder="https://yourwebsite.com"
                    />
                  ) : (
                    <div className="flex items-center gap-2 mt-1">
                      <Globe className="w-4 h-4 text-muted-foreground" />
                      {profile?.website ? (
                        <a 
                          href={profile.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-sm text-primary hover:underline"
                        >
                          {profile.website}
                        </a>
                      ) : (
                        <span className="text-sm text-muted-foreground">Not set</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {isEditing && (
            <div className="flex gap-3 mt-6 pt-6 border-t">
              <Button onClick={handleSaveProfile} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Changes'}
              </Button>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Writing Goals */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coffee className="w-5 h-5" />
            Writing Goals
          </CardTitle>
          <CardDescription>
            Set your daily and weekly writing targets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="daily_words">Daily Word Goal</Label>
              {isEditing ? (
                <Input
                  id="daily_words"
                  type="number"
                  value={formData.daily_words}
                  onChange={(e) => setFormData(prev => ({ ...prev, daily_words: parseInt(e.target.value) || 0 }))}
                  min="0"
                  step="100"
                />
              ) : (
                <div className="text-sm text-muted-foreground mt-1">
                  {profile?.writing_goals?.daily_words?.toLocaleString() || 1000} words
                </div>
              )}
            </div>
            <div>
              <Label htmlFor="weekly_hours">Weekly Hours Goal</Label>
              {isEditing ? (
                <Input
                  id="weekly_hours"
                  type="number"
                  value={formData.weekly_hours}
                  onChange={(e) => setFormData(prev => ({ ...prev, weekly_hours: parseInt(e.target.value) || 0 }))}
                  min="0"
                  max="168"
                />
              ) : (
                <div className="text-sm text-muted-foreground mt-1">
                  {profile?.writing_goals?.weekly_hours || 10} hours
                </div>
              )}
            </div>
            <div>
              <Label htmlFor="genre_focus">Primary Genre</Label>
              {isEditing ? (
                <Select value={formData.genre_focus} onValueChange={(value) => setFormData(prev => ({ ...prev, genre_focus: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select genre" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Fiction">Fiction</SelectItem>
                    <SelectItem value="Fantasy">Fantasy</SelectItem>
                    <SelectItem value="Science Fiction">Science Fiction</SelectItem>
                    <SelectItem value="Romance">Romance</SelectItem>
                    <SelectItem value="Mystery">Mystery</SelectItem>
                    <SelectItem value="Thriller">Thriller</SelectItem>
                    <SelectItem value="Horror">Horror</SelectItem>
                    <SelectItem value="Non-fiction">Non-fiction</SelectItem>
                    <SelectItem value="Biography">Biography</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="text-sm text-muted-foreground mt-1">
                  {profile?.writing_goals?.genre_focus || 'Not set'}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Privacy & Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Privacy & Preferences
          </CardTitle>
          <CardDescription>
            Control your privacy settings and notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="public_profile">Public Profile</Label>
              <p className="text-xs text-muted-foreground">Allow others to view your writing stats</p>
            </div>
            <Switch
              id="public_profile"
              checked={formData.public_profile}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, public_profile: checked }))}
              disabled={!isEditing}
            />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email_notifications">Email Notifications</Label>
              <p className="text-xs text-muted-foreground">Receive updates about your projects</p>
            </div>
            <Switch
              id="email_notifications"
              checked={formData.email_notifications}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, email_notifications: checked }))}
              disabled={!isEditing}
            />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="writing_reminders">Writing Reminders</Label>
              <p className="text-xs text-muted-foreground">Get reminded to reach your daily goals</p>
            </div>
            <Switch
              id="writing_reminders"
              checked={formData.writing_reminders}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, writing_reminders: checked }))}
              disabled={!isEditing}
            />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="beta_features">Beta Features</Label>
              <p className="text-xs text-muted-foreground">Get early access to new features</p>
            </div>
            <Switch
              id="beta_features"
              checked={formData.beta_features}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, beta_features: checked }))}
              disabled={!isEditing}
            />
          </div>
        </CardContent>
      </Card>

      {/* Writing Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Feather className="w-5 h-5" />
            Writing Statistics
          </CardTitle>
          <CardDescription>
            Your writing progress and achievements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 rounded-lg bg-muted/50">
              <div className="text-2xl font-bold text-primary">
                {profile?.stats?.projects_count || 0}
              </div>
              <div className="text-xs text-muted-foreground">Projects</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-muted/50">
              <div className="text-2xl font-bold text-primary">
                {profile?.stats?.total_words?.toLocaleString() || 0}
              </div>
              <div className="text-xs text-muted-foreground">Total Words</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-muted/50">
              <div className="text-2xl font-bold text-primary">
                {profile?.stats?.days_active || 0}
              </div>
              <div className="text-xs text-muted-foreground">Active Days</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-muted/50">
              <div className="text-2xl font-bold text-primary">
                {profile?.stats?.member_since || 'N/A'}
              </div>
              <div className="text-xs text-muted-foreground">Member Since</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="w-5 h-5" />
            Account Actions
          </CardTitle>
          <CardDescription>
            Manage your account and data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Export Data</Label>
                <p className="text-xs text-muted-foreground">Download all your writing data</p>
              </div>
              <Button variant="outline" size="sm" disabled>
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div>
                <Label>Sign Out</Label>
                <p className="text-xs text-muted-foreground">Sign out of your account</p>
              </div>
              <Button variant="outline" size="sm" onClick={handleSignOut}>
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-destructive">Delete Account</Label>
                <p className="text-xs text-muted-foreground">Permanently delete your account and all data</p>
              </div>
              <Button variant="destructive" size="sm" disabled>
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}