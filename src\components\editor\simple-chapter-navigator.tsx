'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useEditorStore } from '@/stores/editor-store'
import { 
  ChevronLeft, 
  Plus, 
  CheckCircle, 
  Circle, 
  Edit3,
  Search,
  FileText,
  PanelLeftClose,
  PanelLeftOpen
} from 'lucide-react'

interface SimpleChapterNavigatorProps {
  projectId: string
  currentChapterId?: string
  onChapterSelect: (chapterId: string, chapterNumber: number) => void
  onCreateChapter: () => void
}

export function SimpleChapterNavigator({ 
  projectId,
  currentChapterId, 
  onChapterSelect,
  onCreateChapter 
}: SimpleChapterNavigatorProps) {
  const { 
    showChapterNavigator, 
    toggleChapterNavigator, 
    chapters 
  } = useEditorStore()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [isCollapsed, setIsCollapsed] = useState(false)
  
  const totalWords = chapters.reduce((sum, chapter) => sum + chapter.wordCount, 0)
  const completedChapters = chapters.filter(ch => ch.status === 'complete').length

  const filteredChapters = chapters.filter(chapter => 
    chapter.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chapter.number.toString().includes(searchTerm)
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'review': return <Edit3 className="h-4 w-4 text-blue-500" />
      case 'writing': return <Edit3 className="h-4 w-4 text-yellow-500" />
      default: return <Circle className="h-4 w-4 text-gray-400" />
    }
  }

  if (!showChapterNavigator) return null
  
  if (isCollapsed) {
    return (
      <div className="h-full w-12 border-r bg-background flex flex-col items-center py-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(false)}
          className="mb-4"
        >
          <PanelLeftOpen className="h-4 w-4" />
        </Button>
        
        <div className="flex-1 flex flex-col items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="p-2"
          >
            <FileText className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full w-80 flex flex-col bg-background border-r">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between mb-3">
          <h2 className="font-semibold text-lg">Chapters</h2>
          <div className="flex items-center gap-1">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsCollapsed(true)}
            >
              <PanelLeftClose className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={toggleChapterNavigator}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Stats */}
        <div className="grid grid-cols-2 gap-2 mb-3">
          <div className="text-center p-2 bg-muted rounded-md">
            <div className="text-lg font-semibold">{chapters.length}</div>
            <div className="text-xs text-muted-foreground">Chapters</div>
          </div>
          <div className="text-center p-2 bg-muted rounded-md">
            <div className="text-lg font-semibold">
              {Math.round(totalWords / 1000)}k
            </div>
            <div className="text-xs text-muted-foreground">Words</div>
          </div>
        </div>
        
        {/* Progress */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{completedChapters}/{chapters.length}</span>
          </div>
          <Progress value={(completedChapters / Math.max(chapters.length, 1)) * 100} className="h-2" />
        </div>
      </div>

      {/* Search and Actions */}
      <div className="p-4 space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search chapters..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-9"
          />
        </div>
        
        <Button onClick={onCreateChapter} size="sm" className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          New Chapter
        </Button>
      </div>

      {/* Chapter List */}
      <ScrollArea className="flex-1 px-4">
        <div className="space-y-2 pb-4">
          {filteredChapters.map((chapter) => (
            <div
              key={chapter.id}
              className={`p-3 rounded-lg cursor-pointer transition-all hover:bg-muted/50 ${
                currentChapterId === chapter.id 
                  ? 'bg-primary/10 border-l-4 border-primary' 
                  : ''
              }`}
              onClick={() => onChapterSelect(chapter.id, chapter.number)}
            >
              <div className="space-y-1.5">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(chapter.status)}
                    <span className="font-medium text-sm">
                      Chapter {chapter.number}
                    </span>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {chapter.wordCount.toLocaleString()}
                  </Badge>
                </div>
                
                <h4 className="text-sm text-muted-foreground">
                  {chapter.title}
                </h4>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}