/**
 * Simplified Theme Hook for BookScribe AI
 * Provides easy theme switching with direct CSS control
 */

'use client';

import { useState, useEffect } from 'react';
import { 
  ThemeId, 
  themeConfigs, 
  applyTheme, 
  getCurrentTheme, 
  switchTheme, 
  getAllThemes, 
  getThemesByMode,
  initializeTheme 
} from '@/lib/themes/theme-applier';

export function useSimpleTheme() {
  const [currentTheme, setCurrentTheme] = useState<ThemeId | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    initializeTheme();
    setCurrentTheme(getCurrentTheme());
  }, []);

  const handleSwitchTheme = (themeId: ThemeId) => {
    switchTheme(themeId);
    setCurrentTheme(themeId);
  };

  const isThemeActive = (themeId: ThemeId): boolean => {
    return currentTheme === themeId;
  };

  const getCurrentMode = () => {
    if (!currentTheme) return null;
    return themeConfigs[currentTheme]?.mode || null;
  };

  return {
    // State
    currentTheme,
    mounted,
    
    // Theme data
    themeConfigs,
    getAllThemes,
    getThemesByMode,
    
    // Actions
    switchTheme: handleSwitchTheme,
    isThemeActive,
    getCurrentMode,
    
    // Utilities
    isDark: () => getCurrentMode() === 'dark',
    isLight: () => getCurrentMode() === 'light',
  };
}