'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FolderOpen,
  FileText,
  BookOpen,
  User,
  MapPin,
  StickyNote,
  Plus,
  MoreVertical,
  Edit3,
  Trash2,
  Copy,
  Eye,
  EyeOff,
  Target,
  Calendar,
  Search,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { DocumentNode, DocumentNodeType, DocumentTreeProps } from '@/types/document-tree';

// Icon mapping for different node types
const getNodeIcon = (node: DocumentNode, isExpanded: boolean = false) => {
  switch (node.type) {
    case 'folder':
      return isExpanded ? <FolderOpen className="w-4 h-4" /> : <Folder className="w-4 h-4" />;
    case 'chapter':
      return <BookOpen className="w-4 h-4" />;
    case 'scene':
      return <FileText className="w-4 h-4" />;
    case 'character':
      return <User className="w-4 h-4" />;
    case 'location':
      return <MapPin className="w-4 h-4" />;
    case 'note':
      return <StickyNote className="w-4 h-4" />;
    default:
      return <FileText className="w-4 h-4" />;
  }
};

// Status color mapping
const getStatusColor = (status?: string) => {
  switch (status) {
    case 'completed': return 'bg-green-500/20 text-green-700 border-green-500/30';
    case 'in-progress': return 'bg-blue-500/20 text-blue-700 border-blue-500/30';
    case 'draft': return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30';
    case 'published': return 'bg-purple-500/20 text-purple-700 border-purple-500/30';
    default: return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
  }
};

interface DocumentTreeNodeProps {
  node: DocumentNode;
  depth: number;
  isSelected: boolean;
  isExpanded: boolean;
  onSelect: (node: DocumentNode) => void;
  onToggleExpanded: (nodeId: string) => void;
  onCreateChild: (parentId: string, type: DocumentNodeType) => void;
  onRename: (nodeId: string, newTitle: string) => void;
  onDelete: (nodeId: string) => void;
  onDuplicate: (nodeId: string) => void;
  showWordCounts: boolean;
  showStatus: boolean;
  readonly: boolean;
}

function DocumentTreeNode({
  node,
  depth,
  isSelected,
  isExpanded,
  onSelect,
  onToggleExpanded,
  onCreateChild,
  onRename,
  onDelete,
  onDuplicate,
  showWordCounts,
  showStatus,
  readonly,
}: DocumentTreeNodeProps) {
  const [isRenaming, setIsRenaming] = useState(false);
  const [renameValue, setRenameValue] = useState(node.title);

  const handleRename = () => {
    if (renameValue.trim() && renameValue !== node.title) {
      onRename(node.id, renameValue.trim());
    }
    setIsRenaming(false);
    setRenameValue(node.title);
  };

  const hasChildren = node.children && node.children.length > 0;
  const canHaveChildren = node.type === 'folder' || node.type === 'chapter';

  return (
    <div className="select-none">
      {/* Node Row */}
      <div
        className={cn(
          'flex items-center gap-1 py-1 px-2 rounded-md cursor-pointer hover:bg-accent/50',
          isSelected && 'bg-primary/10 border border-primary/20',
          'transition-colors duration-150'
        )}
        style={{ paddingLeft: `${depth * 16 + 8}px` }}
        onClick={() => onSelect(node)}
      >
        {/* Expand/Collapse Button */}
        <Button
          variant="ghost"
          size="sm"
          className="w-5 h-5 p-0 hover:bg-transparent"
          onClick={(e) => {
            e.stopPropagation();
            if (hasChildren || canHaveChildren) {
              onToggleExpanded(node.id);
            }
          }}
          disabled={!hasChildren && !canHaveChildren}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )
          ) : canHaveChildren ? (
            <div className="w-3 h-3" />
          ) : null}
        </Button>

        {/* Icon */}
        <div className="text-muted-foreground">
          {getNodeIcon(node, isExpanded)}
        </div>

        {/* Title */}
        <div className="flex-1 min-w-0">
          {isRenaming ? (
            <Input
              value={renameValue}
              onChange={(e) => setRenameValue(e.target.value)}
              onBlur={handleRename}
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleRename();
                if (e.key === 'Escape') {
                  setIsRenaming(false);
                  setRenameValue(node.title);
                }
              }}
              className="h-6 py-1 text-sm"
              autoFocus
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <span
              className={cn(
                'text-sm truncate',
                isSelected ? 'font-medium' : 'font-normal',
                node.type === 'folder' && 'font-semibold'
              )}
              title={node.title}
            >
              {node.title}
              {node.chapterNumber && ` (${node.chapterNumber})`}
            </span>
          )}
        </div>

        {/* Metadata */}
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          {/* Word Count */}
          {showWordCounts && node.wordCount !== undefined && (
            <span className="tabular-nums">
              {node.wordCount.toLocaleString()}
            </span>
          )}

          {/* Target Progress for Chapters */}
          {node.type === 'chapter' && node.targetWordCount && node.wordCount !== undefined && (
            <Badge variant="outline" className="text-xs px-1 py-0">
              {Math.round((node.wordCount / node.targetWordCount) * 100)}%
            </Badge>
          )}

          {/* Status */}
          {showStatus && node.status && (
            <Badge className={cn('text-xs px-1 py-0', getStatusColor(node.status))}>
              {node.status}
            </Badge>
          )}

          {/* Actions Menu */}
          {!readonly && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-5 h-5 p-0 hover:bg-accent"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="w-3 h-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => setIsRenaming(true)}>
                  <Edit3 className="w-4 h-4 mr-2" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDuplicate(node.id)}>
                  <Copy className="w-4 h-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                
                {canHaveChildren && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onCreateChild(node.id, 'folder')}>
                      <Folder className="w-4 h-4 mr-2" />
                      Add Folder
                    </DropdownMenuItem>
                    {node.type === 'folder' && (
                      <DropdownMenuItem onClick={() => onCreateChild(node.id, 'chapter')}>
                        <BookOpen className="w-4 h-4 mr-2" />
                        Add Chapter
                      </DropdownMenuItem>
                    )}
                    {node.type === 'chapter' && (
                      <DropdownMenuItem onClick={() => onCreateChild(node.id, 'scene')}>
                        <FileText className="w-4 h-4 mr-2" />
                        Add Scene
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={() => onCreateChild(node.id, 'note')}>
                      <StickyNote className="w-4 h-4 mr-2" />
                      Add Note
                    </DropdownMenuItem>
                  </>
                )}
                
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onDelete(node.id)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Children */}
      {isExpanded && hasChildren && (
        <div>
          {node.children!.map((child) => (
            <DocumentTreeNode
              key={child.id}
              node={child}
              depth={depth + 1}
              isSelected={child.id === node.id} // This should be passed from parent
              isExpanded={false} // This should be managed by parent
              onSelect={onSelect}
              onToggleExpanded={onToggleExpanded}
              onCreateChild={onCreateChild}
              onRename={onRename}
              onDelete={onDelete}
              onDuplicate={onDuplicate}
              showWordCounts={showWordCounts}
              showStatus={showStatus}
              readonly={readonly}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export function DocumentTree({
  projectId,
  initialNodes = [],
  selectedNodeId,
  onNodeSelect,
  onNodeCreate,
  onNodeUpdate,
  onNodeDelete,
  showWordCounts = true,
  showStatus = true,
  allowDragDrop = true,
  readonly = false,
}: DocumentTreeProps) {
  const [nodes, setNodes] = useState<DocumentNode[]>(initialNodes);
  const [expandedNodeIds, setExpandedNodeIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');

  // Build hierarchical tree structure
  const treeNodes = useMemo(() => {
    const nodeMap = new Map(nodes.map(node => [node.id, { ...node, children: [] }]));
    const rootNodes: DocumentNode[] = [];

    // Build parent-child relationships
    for (const node of nodes) {
      const nodeWithChildren = nodeMap.get(node.id)!;
      if (node.parentId && nodeMap.has(node.parentId)) {
        const parent = nodeMap.get(node.parentId)!;
        if (!parent.children) parent.children = [];
        parent.children.push(nodeWithChildren);
      } else {
        rootNodes.push(nodeWithChildren);
      }
    }

    // Sort by order
    const sortNodes = (nodes: DocumentNode[]) => {
      nodes.sort((a, b) => (a.order || 0) - (b.order || 0));
      nodes.forEach(node => {
        if (node.children) sortNodes(node.children);
      });
    };
    sortNodes(rootNodes);

    return rootNodes;
  }, [nodes]);

  // Filter nodes based on search query
  const filteredNodes = useMemo(() => {
    if (!searchQuery.trim()) return treeNodes;

    const matchesSearch = (node: DocumentNode): boolean => {
      return node.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
             (node.children && node.children.some(child => matchesSearch(child)));
    };

    const filterTree = (nodes: DocumentNode[]): DocumentNode[] => {
      return nodes
        .map(node => ({
          ...node,
          children: node.children ? filterTree(node.children) : undefined
        }))
        .filter(matchesSearch);
    };

    return filterTree(treeNodes);
  }, [treeNodes, searchQuery]);

  const handleNodeSelect = useCallback((node: DocumentNode) => {
    onNodeSelect?.(node);
  }, [onNodeSelect]);

  const handleToggleExpanded = useCallback((nodeId: string) => {
    setExpandedNodeIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  const handleCreateChild = useCallback((parentId: string, type: DocumentNodeType) => {
    const newNode: DocumentNode = {
      id: `temp-${Date.now()}`,
      title: `New ${type}`,
      type,
      parentId,
      order: 0, // Should be calculated based on siblings
      status: 'draft',
      wordCount: 0,
    };
    
    setNodes(prev => [...prev, newNode]);
    onNodeCreate?.(newNode);
  }, [onNodeCreate]);

  const handleRename = useCallback((nodeId: string, newTitle: string) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId ? { ...node, title: newTitle } : node
    ));
    const updatedNode = nodes.find(n => n.id === nodeId);
    if (updatedNode) {
      onNodeUpdate?.({ ...updatedNode, title: newTitle });
    }
  }, [nodes, onNodeUpdate]);

  const handleDelete = useCallback((nodeId: string) => {
    setNodes(prev => prev.filter(node => node.id !== nodeId && node.parentId !== nodeId));
    onNodeDelete?.(nodeId);
  }, [onNodeDelete]);

  const handleDuplicate = useCallback((nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      const duplicatedNode: DocumentNode = {
        ...node,
        id: `temp-${Date.now()}`,
        title: `${node.title} (Copy)`,
        order: (node.order || 0) + 1,
      };
      setNodes(prev => [...prev, duplicatedNode]);
      onNodeCreate?.(duplicatedNode);
    }
  }, [nodes, onNodeCreate]);

  const handleCreateRoot = useCallback((type: DocumentNodeType) => {
    handleCreateChild('', type);
  }, [handleCreateChild]);

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-sm">Document Tree</h3>
          {!readonly && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Plus className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleCreateRoot('folder')}>
                  <Folder className="w-4 h-4 mr-2" />
                  Add Folder
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleCreateRoot('chapter')}>
                  <BookOpen className="w-4 h-4 mr-2" />
                  Add Chapter
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleCreateRoot('note')}>
                  <StickyNote className="w-4 h-4 mr-2" />
                  Add Note
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 h-8 text-sm"
          />
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full px-3 pb-3">
          {filteredNodes.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No documents found</p>
              {!readonly && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleCreateRoot('chapter')}
                  className="mt-2"
                >
                  Create your first chapter
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-1">
              {filteredNodes.map((node) => (
                <DocumentTreeNode
                  key={node.id}
                  node={node}
                  depth={0}
                  isSelected={selectedNodeId === node.id}
                  isExpanded={expandedNodeIds.has(node.id)}
                  onSelect={handleNodeSelect}
                  onToggleExpanded={handleToggleExpanded}
                  onCreateChild={handleCreateChild}
                  onRename={handleRename}
                  onDelete={handleDelete}
                  onDuplicate={handleDuplicate}
                  showWordCounts={showWordCounts}
                  showStatus={showStatus}
                  readonly={readonly}
                />
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

export default DocumentTree;