'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export default function TestComponentsPage() {
  const [step, setStep] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const testSteps = [
    {
      name: 'Basic UI Components',
      test: () => (
        <div className="space-y-2">
          <Button>Test Button</Button>
          <Card className="p-4">Test Card</Card>
        </div>
      )
    },
    {
      name: 'Editor Store',
      test: () => {
        try {
          const { useEditorStore } = require('@/stores/editor-store')
          const store = useEditorStore.getState()
          return <div>Store loaded: {JSON.stringify({ chapters: store.chapters.length })}</div>
        } catch (e) {
          throw new Error(`Store error: ${e.message}`)
        }
      }
    },
    {
      name: 'Enhanced Chapter Navigator',
      test: () => {
        try {
          const { EnhancedChapterNavigator } = require('@/components/editor/enhanced-chapter-navigator')
          return <div>EnhancedChapterNavigator component loaded</div>
        } catch (e) {
          throw new Error(`Navigator error: ${e.message}`)
        }
      }
    },
    {
      name: 'Monaco Editor',
      test: () => {
        try {
          const { LazyMonacoEditor } = require('@/components/editor/lazy-monaco-editor')
          return <div>LazyMonacoEditor component loaded</div>
        } catch (e) {
          throw new Error(`Monaco error: ${e.message}`)
        }
      }
    },
    {
      name: 'Enhanced Sidebar',
      test: () => {
        try {
          const { EnhancedSidebarPanel } = require('@/components/editor/enhanced-sidebar-panel')
          return <div>EnhancedSidebarPanel component loaded</div>
        } catch (e) {
          throw new Error(`Sidebar error: ${e.message}`)
        }
      }
    }
  ]

  const runTest = (index: number) => {
    try {
      setError(null)
      const result = testSteps[index].test()
      setStep(index + 1)
      return result
    } catch (e) {
      setError(e.message)
      return null
    }
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Component Test Page</h1>
      
      <div className="space-y-4">
        {testSteps.map((testStep, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-semibold">{testStep.name}</h3>
                {index < step && (
                  <div className="mt-2 text-sm text-green-600">
                    ✓ Loaded successfully
                  </div>
                )}
                {index === step - 1 && error && (
                  <div className="mt-2 text-sm text-red-600">
                    ✗ Error: {error}
                  </div>
                )}
              </div>
              {index === step && (
                <Button onClick={() => runTest(index)} size="sm">
                  Test
                </Button>
              )}
            </div>
            
            {index < step && !error && (
              <div className="mt-2 p-2 bg-gray-100 rounded text-sm">
                {runTest(index - 1)}
              </div>
            )}
          </Card>
        ))}
      </div>

      {step >= testSteps.length && (
        <Card className="mt-6 p-6 bg-green-50 border-green-200">
          <h2 className="text-lg font-semibold text-green-800">All components loaded successfully!</h2>
          <p className="mt-2 text-green-700">
            You can now safely navigate to the <a href="/knowledge-demo" className="underline">knowledge-demo page</a>.
          </p>
        </Card>
      )}
    </div>
  )
}