"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useResizablePanel } from "@/hooks/use-resizable-panel";
import { 
  PenTool, 
  BookOpen, 
  Users, 
  Lightbulb, 
  Zap, 
  BarChart3,
  FileText,
  MessageSquare,
  Sparkles,
  ChevronRight,
  Play,
  Pause
} from "lucide-react";

const sampleContent = `Chapter 1: The Crystal's Call

Aria <PERSON> pressed her palm against the ancient crystal embedded in the academy's wall, feeling the familiar tingle of magic coursing through her veins. The Aethermoor Academy for Mystical Arts had been her home for three years, but today something felt different.

"Still trying to unlock the Heartstone's secrets?" <PERSON>'s deep voice echoed behind her. The dwarf instructor approached with his characteristic measured steps, his silver beard catching the morning light.

"There's something here, Master <PERSON>," <PERSON> whispered, her green eyes fixed on the pulsing crystal. "I can feel it calling to me."

The crystal's glow intensified, casting dancing shadows across the stone corridor. Other students hurried past, their robes rustling as they headed to morning classes, but <PERSON> remained transfixed.

"The Heartstone has been silent for centuries," <PERSON> said, his weathered hand stroking his beard thoughtfully. "Legend speaks of a chosen one who would awaken its power, but—"

A brilliant flash erupted from the crystal, sending waves of energy rippling through the academy. <PERSON> stumbled backward, her vision filled with images of distant realms, ancient battles, and a darkness spreading across the land.

"The prophecy," she gasped, her voice barely audible. "It's beginning."`;

const aiSuggestions = [
  {
    type: "dialogue",
    suggestion: "Consider adding more emotional depth to Aria's response. Perhaps show her fear or excitement through physical reactions.",
    position: "Line 12"
  },
  {
    type: "description",
    suggestion: "The crystal's description could be enhanced with more sensory details - sound, temperature, or texture.",
    position: "Line 8"
  },
  {
    type: "pacing",
    suggestion: "This scene moves quickly to the revelation. Consider building more tension before the crystal activates.",
    position: "Overall"
  },
  {
    type: "character",
    suggestion: "Marcus's reaction seems understated for such a momentous event. Show more surprise or concern.",
    position: "Line 15"
  }
];

const storyBibleData = {
  characters: [
    { name: "Aria Stormwind", role: "Protagonist", status: "Active in scene" },
    { name: "Marcus Ironforge", role: "Mentor", status: "Active in scene" },
    { name: "Zara Nightwhisper", role: "Antagonist", status: "Not yet introduced" }
  ],
  locations: [
    { name: "Aethermoor Academy", description: "Ancient magical school", relevance: "Current setting" },
    { name: "Crystal Caverns", description: "Source of Heartstone power", relevance: "Future location" }
  ],
  plotThreads: [
    { thread: "Aria's awakening powers", status: "Active" },
    { thread: "The ancient prophecy", status: "Just revealed" },
    { thread: "Rising darkness", status: "Foreshadowed" }
  ]
};

export function DemoEditor() {
  const [isTyping, setIsTyping] = useState(false);
  const [currentContent, setCurrentContent] = useState("");
  const [wordCount, setWordCount] = useState(0);
  const [selectedSuggestion, setSelectedSuggestion] = useState<number | null>(null);
  
  // Add resizable panel for sidebar
  const { width: sidebarWidth, ResizeHandle, isResizing } = useResizablePanel({
    initialWidth: 320,
    minWidth: 280,
    maxWidth: 500
  });

  useEffect(() => {
    // Simulate typing effect
    if (isTyping) {
      let index = 0;
      const interval = setInterval(() => {
        if (index < sampleContent.length) {
          setCurrentContent(sampleContent.slice(0, index + 1));
          setWordCount(sampleContent.slice(0, index + 1).split(' ').length);
          index++;
        } else {
          setIsTyping(false);
          clearInterval(interval);
        }
      }, 20);
      return () => clearInterval(interval);
    }
  }, [isTyping]);

  const startTyping = () => {
    setCurrentContent("");
    setWordCount(0);
    setIsTyping(true);
  };

  return (
    <div className="max-w-7xl mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl flex items-center gap-2">
              <PenTool className="w-6 h-6 text-primary" />
              AI-Powered Writing Interface
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="border-primary/50 text-primary">
                Interactive Demo
              </Badge>
              <Button
                onClick={startTyping}
                disabled={isTyping}
                variant="default"
              >
                {isTyping ? (
                  <>
                    <Pause className="w-4 h-4 mr-2" />
                    Writing...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Start Demo
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="flex gap-4 relative">
            {/* Main Editor */}
            <div className="flex-1 space-y-4" style={{ marginRight: sidebarWidth + 16 }}>
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Chapter 1: The Crystal's Call</h3>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>Words: {wordCount.toLocaleString()}</span>
                  <span>Target: 3,500</span>
                  <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-gradient-to-r from-primary to-primary/80 transition-all duration-300"
                      style={{ width: `${Math.min((wordCount / 3500) * 100, 100)}%` }}
                    />
                  </div>
                </div>
              </div>

              <Card className="border-border bg-muted/50">
                <CardContent className="p-0">
                  <div className="relative">
                    <textarea
                      value={currentContent}
                      onChange={(e) => setCurrentContent(e.target.value)}
                      className="w-full h-96 p-6 bg-transparent border-none resize-none focus:outline-none text-foreground font-editor text-sm leading-relaxed"
                      placeholder="Start writing your story..."
                    />
                    {isTyping && (
                      <div className="absolute bottom-4 right-4">
                        <div className="flex items-center gap-2 px-3 py-1 bg-primary/20 border border-primary/30 rounded-full text-xs text-primary">
                          <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                          AI Writing...
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* AI Suggestions Panel */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Lightbulb className="w-5 h-5 text-primary" />
                    AI Suggestions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {aiSuggestions.map((suggestion, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg border cursor-pointer transition-all ${
                          selectedSuggestion === index
                            ? 'border-primary/50 bg-primary/10'
                            : 'border-border bg-muted/30 hover:border-primary/30'
                        }`}
                        onClick={() => setSelectedSuggestion(selectedSuggestion === index ? null : index)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <Badge variant="outline" className="text-xs">
                                {suggestion.type}
                              </Badge>
                              <span className="text-xs text-muted-foreground">{suggestion.position}</span>
                            </div>
                            <p className="text-sm text-foreground">{suggestion.suggestion}</p>
                          </div>
                          <Button size="sm" variant="ghost" className="text-primary hover:bg-primary/10">
                            Apply
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Resizable Sidebar */}
            <div 
              className="absolute top-0 right-0 bottom-0 bg-background border-l border-border"
              style={{ width: sidebarWidth }}
            >
              <ResizeHandle />
              <div className="p-4 space-y-4 h-full overflow-y-auto">
                <Tabs defaultValue="story-bible" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="story-bible">Story Bible</TabsTrigger>
                    <TabsTrigger value="chapters">Chapters</TabsTrigger>
                  </TabsList>

                  <TabsContent value="story-bible" className="space-y-4">
                    <Card className="border-border bg-card">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Characters
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {storyBibleData.characters.map((character, index) => (
                            <div key={index} className="p-2 rounded border border-border bg-muted/50">
                              <div className="flex items-center justify-between">
                                <span className="font-medium text-sm">{character.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {character.role}
                                </Badge>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">{character.status}</p>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-border bg-card">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <BookOpen className="w-4 h-4" />
                          Plot Threads
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {storyBibleData.plotThreads.map((thread, index) => (
                            <div key={index} className="p-2 rounded border border-border bg-muted/50">
                              <div className="flex items-center justify-between">
                                <span className="text-sm">{thread.thread}</span>
                                <Badge 
                                  variant="outline" 
                                  className={`text-xs ${
                                    thread.status === 'Active' 
                                      ? 'border-green-500/50 text-green-400' 
                                      : ''
                                  }`}
                                >
                                  {thread.status}
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="chapters" className="space-y-4">
                    <Card className="border-border bg-card">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <FileText className="w-4 h-4" />
                          Chapter Outline
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ScrollArea className="h-64">
                          <div className="space-y-2">
                            {[
                              { num: 1, title: "The Crystal's Call", status: "Writing", words: wordCount },
                              { num: 2, title: "Shadows Awakening", status: "Planned", words: 0 },
                              { num: 3, title: "The First Trial", status: "Outlined", words: 0 },
                              { num: 4, title: "Ancient Secrets", status: "Planned", words: 0 },
                              { num: 5, title: "The Gathering Storm", status: "Planned", words: 0 }
                            ].map((chapter) => (
                              <div 
                                key={chapter.num} 
                                className={`p-3 rounded border cursor-pointer transition-all ${
                                  chapter.num === 1 
                                    ? 'border-primary/50 bg-primary/10' 
                                    : 'border-border bg-muted/30 hover:border-primary/30'
                                }`}
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <span className="font-medium text-sm">Ch. {chapter.num}</span>
                                    <p className="text-xs text-muted-foreground">{chapter.title}</p>
                                  </div>
                                  <div className="text-right">
                                    <Badge 
                                      variant="outline" 
                                      className={`text-xs ${
                                        chapter.status === 'Writing' 
                                          ? 'border-primary/50 text-primary' 
                                          : ''
                                      }`}
                                    >
                                      {chapter.status}
                                    </Badge>
                                    <p className="text-xs text-muted-foreground mt-1">{chapter.words} words</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>

                {/* Quick Stats */}
                <Card className="border-border bg-card">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <BarChart3 className="w-4 h-4" />
                      Writing Stats
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Today's Progress</span>
                        <span className="text-sm font-medium">{wordCount} words</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Daily Goal</span>
                        <span className="text-sm font-medium">2,000 words</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Project Progress</span>
                        <span className="text-sm font-medium">1.2%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
